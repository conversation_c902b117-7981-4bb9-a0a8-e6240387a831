"""
API客户端模块
负责与外部API服务进行通信，获取智能回复内容
"""

import requests
import json
import time
import logging
from typing import Dict, Optional, Any
from dataclasses import dataclass
from ..config import config


@dataclass
class QueryRequest:
    """查询请求数据结构"""
    message: str
    sender: str
    chat_type: str
    chat_name: str
    timestamp: str
    custom_prompt: Optional[str] = None


@dataclass
class QueryResponse:
    """查询响应数据结构"""
    success: bool
    reply: Optional[str] = None
    should_reply: bool = True
    error_message: Optional[str] = None
    processing_time: Optional[float] = None


class APIClient:
    """API客户端类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'WxAutoML/1.0'
        })
    
    def query(self, request: QueryRequest) -> QueryResponse:
        """
        发送查询请求到API服务
        
        Args:
            request: 查询请求对象
            
        Returns:
            QueryResponse: 查询响应对象
        """
        start_time = time.time()
        
        # 构建请求数据
        payload = {
            "query": request.message,
            "max_results": 5
        }
        
        # 如果有自定义提示词，添加到请求中
        if request.custom_prompt:
            payload["custom_prompt"] = request.custom_prompt
        
        # 添加上下文信息
        payload["context"] = {
            "sender": request.sender,
            "chat_type": request.chat_type,
            "chat_name": request.chat_name,
            "timestamp": request.timestamp
        }
        
        try:
            response = self._make_request(payload)
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                return QueryResponse(
                    success=True,
                    reply=data.get('answer', ''),
                    should_reply=True,
                    processing_time=processing_time
                )
            else:
                error_msg = f"API请求失败: HTTP {response.status_code}"
                self.logger.error(f"{error_msg}, 响应: {response.text}")
                return QueryResponse(
                    success=False,
                    error_message=error_msg,
                    processing_time=processing_time
                )
                
        except requests.exceptions.Timeout:
            error_msg = "API请求超时"
            self.logger.error(error_msg)
            return QueryResponse(
                success=False,
                error_message=error_msg,
                processing_time=time.time() - start_time
            )
            
        except requests.exceptions.ConnectionError:
            error_msg = "API连接失败"
            self.logger.error(error_msg)
            return QueryResponse(
                success=False,
                error_message=error_msg,
                processing_time=time.time() - start_time
            )
            
        except Exception as e:
            error_msg = f"API请求异常: {str(e)}"
            self.logger.error(error_msg)
            return QueryResponse(
                success=False,
                error_message=error_msg,
                processing_time=time.time() - start_time
            )
    
    def _make_request(self, payload: Dict[str, Any]) -> requests.Response:
        """
        执行HTTP请求，包含重试逻辑
        
        Args:
            payload: 请求负载
            
        Returns:
            requests.Response: HTTP响应对象
        """
        url = config.get_api_url()
        last_exception = None
        
        for attempt in range(config.api_config.max_retries):
            try:
                self.logger.debug(f"API请求尝试 {attempt + 1}/{config.api_config.max_retries}")
                
                response = self.session.post(
                    url,
                    json=payload,
                    timeout=config.api_config.timeout
                )
                
                # 如果请求成功，直接返回
                if response.status_code == 200:
                    return response
                
                # 如果是客户端错误（4xx），不重试
                if 400 <= response.status_code < 500:
                    return response
                
                # 服务器错误（5xx），记录并准备重试
                self.logger.warning(f"服务器错误 {response.status_code}，准备重试")
                
            except (requests.exceptions.Timeout, 
                    requests.exceptions.ConnectionError) as e:
                last_exception = e
                self.logger.warning(f"请求失败: {str(e)}，准备重试")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < config.api_config.max_retries - 1:
                time.sleep(config.api_config.retry_delay * (attempt + 1))
        
        # 所有重试都失败了，抛出最后的异常
        if last_exception:
            raise last_exception
        else:
            # 如果没有异常但仍然失败，返回最后的响应
            return response
    
    def health_check(self) -> bool:
        """
        检查API服务健康状态
        
        Returns:
            bool: 服务是否健康
        """
        try:
            # 使用系统状态端点检查健康状态
            status_url = f"{config.api_config.base_url}/api/v1/status"
            response = self.session.get(status_url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('status') == 'ok'
            
            return False
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {str(e)}")
            return False
    
    def close(self):
        """关闭会话"""
        self.session.close()


# 全局API客户端实例
api_client = APIClient()

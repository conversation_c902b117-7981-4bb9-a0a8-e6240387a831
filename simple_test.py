#!/usr/bin/env python3
"""
简化测试脚本
用于快速测试微信消息监听功能
"""

import time
from wxauto import WeChat

def simple_message_test():
    """简化的消息测试"""
    print("🧪 简化消息监听测试")
    print("="*50)
    
    try:
        # 初始化微信
        print("📱 初始化微信...")
        wx = WeChat()
        print("✅ 微信初始化成功")
        
        # 定义消息回调函数
        def message_callback(msg, chat):
            print(f"\n🎉 收到消息!")
            print(f"   发送者: {msg.sender}")
            print(f"   内容: {msg.content}")
            print(f"   类型: {msg.type}")
            print(f"   属性: {msg.attr}")
            
            # 获取聊天信息
            try:
                chat_info = msg.chat_info()
                print(f"   聊天名称: {chat_info.get('chat_name', 'Unknown')}")
                print(f"   聊天类型: {chat_info.get('chat_type', 'Unknown')}")
            except:
                print(f"   聊天对象: {chat}")
        
        # 测试不同的联系人名称
        test_names = [
            "冯子晋1568512996",
            "文件传输助手",
            # 可以添加其他可能的名称
        ]
        
        print(f"\n📡 尝试添加监听...")
        success_count = 0
        
        for name in test_names:
            try:
                print(f"   尝试监听: {name}")
                result = wx.AddListenChat(nickname=name, callback=message_callback)
                if result:
                    print(f"   ✅ 成功添加监听: {name}")
                    success_count += 1
                else:
                    print(f"   ❌ 添加监听失败: {name}")
            except Exception as e:
                print(f"   ❌ 监听异常: {name} - {str(e)}")
        
        if success_count == 0:
            print("\n❌ 没有成功添加任何监听")
            print("💡 建议:")
            print("   1. 确保微信中有与这些联系人的聊天记录")
            print("   2. 手动打开聊天窗口")
            print("   3. 检查联系人名称是否正确")
            return
        
        # 启动监听
        print(f"\n🚀 启动监听 ({success_count} 个目标)...")
        wx.StartListening()
        
        print("💡 测试说明:")
        print("   1. 现在请向监听的联系人发送消息")
        print("   2. 观察控制台是否有消息输出")
        print("   3. 按 Ctrl+C 停止测试")
        print("\n⏰ 开始监听...")
        
        # 保持监听状态
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 停止监听...")
            wx.StopListening()
            print("✅ 测试结束")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print("💡 请确保微信客户端已启动并登录")

def check_wechat_status():
    """检查微信状态"""
    print("🔍 检查微信状态...")
    
    try:
        wx = WeChat()
        print("✅ 微信连接正常")
        
        # 尝试获取一些基本信息
        try:
            # 这里可以添加更多状态检查
            print("📱 微信客户端状态正常")
        except Exception as e:
            print(f"⚠️ 状态检查警告: {str(e)}")
            
    except Exception as e:
        print(f"❌ 微信连接失败: {str(e)}")
        return False
    
    return True

def manual_chat_test():
    """手动聊天测试"""
    print("🔧 手动聊天测试")
    print("="*50)
    
    try:
        wx = WeChat()
        
        # 让用户输入联系人名称
        contact_name = input("请输入要测试的联系人名称: ").strip()
        if not contact_name:
            print("❌ 联系人名称不能为空")
            return
        
        print(f"🎯 尝试连接到: {contact_name}")
        
        try:
            chat = wx.ChatWith(contact_name)
            if chat:
                print(f"✅ 成功连接到: {contact_name}")
                
                # 尝试获取消息
                try:
                    messages = chat.GetAllMessage()
                    print(f"📨 消息数量: {len(messages)}")
                    
                    if messages:
                        print("📝 最近消息:")
                        for msg in messages[-3:]:
                            print(f"   {msg.sender}: {msg.content[:30]}...")
                except Exception as e:
                    print(f"⚠️ 获取消息失败: {str(e)}")
                
                # 测试监听
                print(f"\n📡 测试监听功能...")
                
                def test_callback(msg, chat):
                    print(f"🎉 监听到消息: {msg.sender} - {msg.content}")
                
                result = wx.AddListenChat(nickname=contact_name, callback=test_callback)
                if result:
                    print("✅ 监听添加成功")
                    wx.StartListening()
                    
                    print("💡 请发送一条测试消息，等待10秒...")
                    time.sleep(10)
                    
                    wx.StopListening()
                    wx.RemoveListenChat(nickname=contact_name)
                    print("✅ 测试完成")
                else:
                    print("❌ 监听添加失败")
                    
            else:
                print(f"❌ 未找到联系人: {contact_name}")
                print("💡 请确保:")
                print("   1. 联系人名称完全正确")
                print("   2. 在微信中有与该联系人的聊天记录")
                print("   3. 手动打开过该聊天窗口")
                
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
            
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")

def main():
    """主函数"""
    print("🧪 微信消息监听简化测试工具")
    print("="*50)
    
    while True:
        print("\n请选择测试:")
        print("1. 检查微信状态")
        print("2. 简化消息监听测试")
        print("3. 手动聊天测试")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-3): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            check_wechat_status()
        elif choice == '2':
            simple_message_test()
        elif choice == '3':
            manual_chat_test()
        else:
            print("❌ 无效选择")
        
        input("\n按回车键继续...")

if __name__ == '__main__':
    main()

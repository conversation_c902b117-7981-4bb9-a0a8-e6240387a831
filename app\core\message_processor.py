"""
消息处理器核心模块
负责消息的解析、过滤和预处理
"""

import logging
from typing import Optional, Dict, Any
from wxauto.msgs import FriendMessage, SelfMessage, SystemMessage, TimeMessage
from ..config import config


class MessageProcessor:
    """消息处理器核心类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def should_process_message(self, msg, chat_name: str) -> bool:
        """
        判断是否应该处理该消息
        
        Args:
            msg: 消息对象
            chat_name: 聊天名称
            
        Returns:
            bool: 是否应该处理
        """
        # 跳过系统消息和时间消息
        if isinstance(msg, (SystemMessage, TimeMessage)):
            self.logger.debug(f"跳过系统/时间消息: {type(msg).__name__}")
            return False
        
        # 跳过自己发送的消息
        if isinstance(msg, SelfMessage):
            self.logger.debug("跳过自己发送的消息")
            return False
        
        # 只处理好友消息
        if not isinstance(msg, FriendMessage):
            self.logger.debug(f"跳过非好友消息: {type(msg).__name__}")
            return False
        
        # 检查消息内容
        if not msg.content or msg.content.strip() == '':
            self.logger.debug("跳过空消息")
            return False
        
        # 跳过特殊消息类型
        special_contents = ['[图片]', '[视频]', '[语音]', '[文件]', '[表情]', '[位置]', '[链接]']
        if msg.content in special_contents:
            self.logger.debug(f"跳过特殊消息类型: {msg.content}")
            return False
        
        # 检查是否在监听目标中
        target = config.get_listen_target(chat_name)
        if not target:
            self.logger.debug(f"聊天不在监听目标中: {chat_name}")
            return False
        
        if not target.enabled:
            self.logger.debug(f"监听目标已禁用: {chat_name}")
            return False
        
        if not target.auto_reply:
            self.logger.debug(f"监听目标未启用自动回复: {chat_name}")
            return False
        
        return True
    
    def extract_message_info(self, msg) -> Dict[str, Any]:
        """
        提取消息信息
        
        Args:
            msg: 消息对象
            
        Returns:
            Dict: 消息信息字典
        """
        try:
            chat_info = msg.chat_info()
            
            return {
                'message_id': getattr(msg, 'id', None),
                'content': msg.content,
                'sender': msg.sender,
                'chat_name': chat_info.get('chat_name', 'Unknown'),
                'chat_type': chat_info.get('chat_type', 'friend'),
                'message_type': msg.type,
                'message_attr': msg.attr,
                'sender_remark': getattr(msg, 'sender_remark', msg.sender)
            }
        except Exception as e:
            self.logger.error(f"提取消息信息失败: {str(e)}")
            return {}
    
    def preprocess_message_content(self, content: str) -> str:
        """
        预处理消息内容
        
        Args:
            content: 原始消息内容
            
        Returns:
            str: 处理后的消息内容
        """
        if not content:
            return ""
        
        # 去除首尾空白
        content = content.strip()
        
        # 处理特殊字符
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        
        # 移除过多的连续空行
        lines = content.split('\n')
        processed_lines = []
        empty_line_count = 0
        
        for line in lines:
            if line.strip() == '':
                empty_line_count += 1
                if empty_line_count <= 2:  # 最多保留2个连续空行
                    processed_lines.append(line)
            else:
                empty_line_count = 0
                processed_lines.append(line)
        
        return '\n'.join(processed_lines)
    
    def is_command_message(self, content: str) -> bool:
        """
        判断是否为命令消息
        
        Args:
            content: 消息内容
            
        Returns:
            bool: 是否为命令消息
        """
        if not content:
            return False
        
        # 检查是否以命令前缀开始
        command_prefixes = ['/', '!', '#', '@bot', '机器人']
        content_lower = content.lower().strip()
        
        for prefix in command_prefixes:
            if content_lower.startswith(prefix.lower()):
                return True
        
        return False
    
    def extract_command(self, content: str) -> Optional[Dict[str, str]]:
        """
        提取命令信息
        
        Args:
            content: 消息内容
            
        Returns:
            Dict: 命令信息，包含command和args
        """
        if not self.is_command_message(content):
            return None
        
        content = content.strip()
        parts = content.split(' ', 1)
        
        command = parts[0].lower()
        args = parts[1] if len(parts) > 1 else ""
        
        # 移除命令前缀
        for prefix in ['/', '!', '#']:
            if command.startswith(prefix):
                command = command[len(prefix):]
                break
        
        return {
            'command': command,
            'args': args.strip()
        }
    
    def should_ignore_sender(self, sender: str) -> bool:
        """
        判断是否应该忽略某个发送者
        
        Args:
            sender: 发送者名称
            
        Returns:
            bool: 是否应该忽略
        """
        # 可以在这里添加黑名单逻辑
        ignore_list = ['系统消息', 'WeChat Team', '微信团队']
        
        return sender in ignore_list
    
    def get_message_priority(self, msg_info: Dict[str, Any]) -> int:
        """
        获取消息优先级
        
        Args:
            msg_info: 消息信息
            
        Returns:
            int: 优先级（数字越大优先级越高）
        """
        priority = 0
        
        # 根据聊天类型调整优先级
        if msg_info.get('chat_type') == 'friend':
            priority += 10
        elif msg_info.get('chat_type') == 'group':
            priority += 5
        
        # 根据消息类型调整优先级
        if msg_info.get('message_type') == 'text':
            priority += 5
        
        # 如果是命令消息，提高优先级
        if self.is_command_message(msg_info.get('content', '')):
            priority += 20
        
        return priority

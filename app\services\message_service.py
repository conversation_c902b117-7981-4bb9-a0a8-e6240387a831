"""
消息处理服务模块
负责处理接收到的微信消息，调用API获取回复并发送响应
"""

import time
import random
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Set
from collections import defaultdict, deque
from wxauto.msgs import FriendMessage, SelfMessage, SystemMessage, TimeMessage
from ..config import config, ListenTarget
from .api_service import api_client, QueryRequest


class RateLimiter:
    """速率限制器"""
    
    def __init__(self):
        self.message_times: Dict[str, deque] = defaultdict(deque)
    
    def can_reply(self, chat_name: str) -> bool:
        """检查是否可以回复"""
        now = datetime.now()
        cutoff_time = now - timedelta(seconds=config.reply_config.rate_limit_seconds)
        
        # 清理过期的时间戳
        times = self.message_times[chat_name]
        while times and times[0] < cutoff_time:
            times.popleft()
        
        # 检查是否超过限制
        return len(times) < config.reply_config.max_replies_per_minute
    
    def record_reply(self, chat_name: str):
        """记录回复时间"""
        self.message_times[chat_name].append(datetime.now())


class MessageHandler:
    """消息处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.rate_limiter = RateLimiter()
        self.processed_messages: Set[str] = set()
        self.last_cleanup = datetime.now()
    
    def handle_message(self, msg, chat):
        """
        处理接收到的消息
        
        Args:
            msg: 消息对象
            chat: 聊天对象
        """
        try:
            # 清理过期的消息ID
            self._cleanup_processed_messages()
            
            # 获取消息基本信息
            message_id = getattr(msg, 'id', None)
            if not message_id:
                return
            
            # 避免重复处理同一条消息
            if message_id in self.processed_messages:
                return
            
            self.processed_messages.add(message_id)
            
            # 获取聊天信息
            chat_info = msg.chat_info()
            chat_name = chat_info.get('chat_name', 'Unknown')
            chat_type = chat_info.get('chat_type', 'friend')
            
            # 记录消息
            self.logger.info(f"收到消息 - 来源: {chat_name}({chat_type}), "
                           f"发送者: {msg.sender}, 内容: {msg.content[:50]}...")

            # 如果是测试目标，额外输出到控制台
            if chat_name == "冯子晋1568512996":
                print(f"\n📨 [测试消息] 来自 {msg.sender}: {msg.content}")
            
            # 过滤不需要处理的消息
            if not self._should_process_message(msg, chat_name):
                return
            
            # 获取监听目标配置
            target = config.get_listen_target(chat_name)
            if not target or not target.auto_reply:
                return
            
            # 检查速率限制
            if not self.rate_limiter.can_reply(chat_name):
                self.logger.warning(f"达到速率限制，跳过回复: {chat_name}")
                return
            
            # 处理消息并生成回复
            self._process_and_reply(msg, chat, target)
            
        except Exception as e:
            self.logger.error(f"处理消息时发生错误: {str(e)}", exc_info=True)
    
    def _should_process_message(self, msg, chat_name: str) -> bool:
        """判断是否应该处理该消息"""
        # 跳过系统消息和时间消息
        if isinstance(msg, (SystemMessage, TimeMessage)):
            return False
        
        # 跳过自己发送的消息
        if isinstance(msg, SelfMessage):
            return False
        
        # 只处理好友消息
        if not isinstance(msg, FriendMessage):
            return False
        
        # 检查消息内容
        if not msg.content or msg.content.strip() == '':
            return False
        
        # 跳过特殊消息类型
        special_contents = ['[图片]', '[视频]', '[语音]', '[文件]', '[表情]']
        if msg.content in special_contents:
            return False
        
        return True
    
    def _process_and_reply(self, msg, chat, target: ListenTarget):
        """处理消息并发送回复"""
        try:
            # 构建查询请求
            request = QueryRequest(
                message=msg.content,
                sender=msg.sender,
                chat_type=target.chat_type,
                chat_name=target.name,
                timestamp=datetime.now().isoformat(),
                custom_prompt=target.custom_prompt
            )
            
            # 调用API获取回复
            self.logger.debug(f"调用API获取回复: {msg.content[:30]}...")
            response = api_client.query(request)
            
            if not response.success:
                self.logger.error(f"API调用失败: {response.error_message}")
                return
            
            if not response.reply or not response.should_reply:
                self.logger.debug("API返回空回复或不需要回复")
                return
            
            # 处理回复内容
            reply_content = self._process_reply_content(response.reply)
            if not reply_content:
                return
            
            # 模拟打字延时
            if config.reply_config.enable_typing_simulation:
                delay = self._calculate_typing_delay(reply_content)
                self.logger.debug(f"模拟打字延时: {delay:.1f}秒")
                time.sleep(delay)
            
            # 发送回复
            self._send_reply(chat, reply_content, msg)
            
            # 记录回复
            self.rate_limiter.record_reply(target.name)
            self.logger.info(f"已回复 - 目标: {target.name}, "
                           f"内容: {reply_content[:50]}...")

            # 如果是测试目标，额外输出到控制台
            if target.name == "冯子晋1568512996":
                print(f"💬 [自动回复] 发送给 {target.name}: {reply_content}")
            
        except Exception as e:
            self.logger.error(f"处理回复时发生错误: {str(e)}", exc_info=True)
    
    def _process_reply_content(self, content: str) -> Optional[str]:
        """处理回复内容"""
        if not content:
            return None
        
        # 去除首尾空白
        content = content.strip()
        
        # 检查长度限制
        max_length = config.reply_config.max_reply_length
        if len(content) > max_length:
            content = content[:max_length] + "..."
            self.logger.warning(f"回复内容过长，已截断到{max_length}字符")
        
        return content
    
    def _calculate_typing_delay(self, content: str) -> float:
        """计算打字延时"""
        # 基础延时
        base_delay = random.uniform(
            config.reply_config.min_delay,
            config.reply_config.max_delay
        )
        
        # 根据内容长度调整延时
        content_factor = min(len(content) / 100, 2.0)  # 最多增加2倍延时
        
        return base_delay + content_factor
    
    def _send_reply(self, chat, content: str, original_msg):
        """发送回复消息"""
        try:
            # 使用引用回复的方式
            if hasattr(original_msg, 'quote'):
                result = original_msg.quote(content)
            else:
                # 如果不支持引用，直接发送消息
                result = chat.SendMsg(content)
            
            # 检查发送结果
            if hasattr(result, 'success') and not result.success:
                self.logger.error(f"发送回复失败: {getattr(result, 'message', '未知错误')}")
            
        except Exception as e:
            self.logger.error(f"发送回复时发生错误: {str(e)}")
            # 尝试备用发送方式
            try:
                chat.SendMsg(content)
            except Exception as e2:
                self.logger.error(f"备用发送方式也失败: {str(e2)}")
    
    def _cleanup_processed_messages(self):
        """清理过期的已处理消息ID"""
        now = datetime.now()
        if now - self.last_cleanup > timedelta(hours=1):
            # 每小时清理一次，保留最近1000条消息ID
            if len(self.processed_messages) > 1000:
                # 简单的清理策略：清空一半
                messages_list = list(self.processed_messages)
                self.processed_messages = set(messages_list[-500:])
            
            self.last_cleanup = now
    
    def get_stats(self) -> Dict:
        """获取处理统计信息"""
        return {
            'processed_messages_count': len(self.processed_messages),
            'rate_limiter_status': {
                chat: len(times) for chat, times in self.rate_limiter.message_times.items()
            }
        }


# 全局消息处理器实例
message_handler = MessageHandler()

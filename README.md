# 微信自动回复系统

基于 wxauto 库开发的智能微信自动回复系统，能够监听微信消息并通过 API 获取智能回复内容。

## 功能特性

- 🎯 **智能监听**: 支持同时监听多个微信联系人/群组
- 🤖 **AI回复**: 集成外部API服务，提供智能回复功能
- ⚡ **实时响应**: 消息响应时间 < 5秒
- 🛡️ **速率控制**: 内置速率限制，避免频繁发送
- 📝 **详细日志**: 完整的消息处理和错误日志
- 🔧 **灵活配置**: 支持运行时配置管理

## 系统要求

- Python 3.8+
- Windows 10/11
- 微信客户端（支持 wxauto 的版本）
- 网络连接（用于API调用）

## 安装依赖

```bash
pip install wxauto requests
```

## 快速开始

### 1. 配置系统

首次运行会自动创建配置文件 `config.json`，或者手动编辑：

```json
{
  "api": {
    "base_url": "http://***********:9000",
    "endpoint": "/api/v1/query"
  },
  "listen_targets": [
    {
      "name": "文件传输助手",
      "enabled": true,
      "auto_reply": true,
      "chat_type": "friend"
    }
  ]
}
```

### 2. 启动服务

```bash
# 直接启动
python run.py start

# 或使用交互式菜单
python run.py
```

### 3. 管理配置

```bash
# 查看状态
python run.py status

# 配置管理
python run.py config
```

## 配置说明

### API配置 (api)

- `base_url`: API服务器地址
- `endpoint`: API端点路径
- `timeout`: 请求超时时间（秒）
- `max_retries`: 最大重试次数
- `retry_delay`: 重试延时（秒）

### 回复配置 (reply)

- `min_delay`: 最小回复延时（秒）
- `max_delay`: 最大回复延时（秒）
- `enable_typing_simulation`: 是否启用打字模拟
- `max_reply_length`: 最大回复长度
- `rate_limit_seconds`: 速率限制时间窗口
- `max_replies_per_minute`: 每分钟最大回复数

### 监听目标 (listen_targets)

- `name`: 联系人/群组名称
- `enabled`: 是否启用监听
- `auto_reply`: 是否自动回复
- `chat_type`: 聊天类型 (friend/group)
- `custom_prompt`: 自定义提示词（可选）

### 日志配置 (logging)

- `level`: 日志级别 (DEBUG/INFO/WARNING/ERROR)
- `file_path`: 日志文件路径
- `max_file_size`: 最大文件大小
- `backup_count`: 备份文件数量
- `enable_console`: 是否启用控制台输出

## API接口

系统调用外部API获取回复内容，API格式：

### 请求格式

```json
{
  "query": "用户消息内容",
  "max_results": 5,
  "context": {
    "sender": "发送者",
    "chat_type": "friend",
    "chat_name": "聊天对象",
    "timestamp": "2024-01-01T12:00:00"
  }
}
```

### 响应格式

```json
{
  "answer": "AI生成的回复内容",
  "sources": [...],
  "processing_time": 1.2
}
```

## 使用示例

### 添加监听目标

```python
from app import config

# 添加好友监听
config.add_listen_target("张三", "friend", enabled=True, auto_reply=True)

# 添加群组监听
config.add_listen_target("工作群", "group", enabled=True, auto_reply=True)
```

### 程序化启动

```python
from app import wx_service

# 启动服务
if wx_service.start():
    print("服务启动成功")
    wx_service.keep_running()
else:
    print("服务启动失败")
```

## 目录结构

```
wxautoMl/
├── app/                          # 应用核心模块
│   ├── __init__.py              # 包初始化
│   ├── config/                  # 配置管理模块
│   │   ├── __init__.py         # 配置包初始化
│   │   ├── settings.py         # 配置数据结构
│   │   ├── config_manager.py   # 配置管理器
│   │   └── config.json         # 配置文件
│   ├── services/               # 服务模块
│   │   ├── __init__.py        # 服务包初始化
│   │   ├── api_service.py     # API服务
│   │   ├── message_service.py # 消息处理服务
│   │   └── wxauto_service.py  # 微信自动化服务
│   ├── core/                  # 核心功能模块
│   │   ├── __init__.py       # 核心包初始化
│   │   ├── message_processor.py # 消息处理器
│   │   ├── reply_generator.py   # 回复生成器
│   │   └── rate_limiter.py     # 速率限制器
│   ├── utils/                 # 工具模块
│   │   ├── __init__.py       # 工具包初始化
│   │   ├── logger.py         # 日志工具
│   │   └── helpers.py        # 辅助工具
│   ├── main.py               # 命令行界面
│   └── test_system.py        # 系统测试
├── logs/                     # 日志目录
├── run.py                   # 启动脚本
├── start.bat               # Windows启动脚本
├── requirements.txt        # 依赖文件
└── README.md              # 说明文档
```

## 注意事项

### 安全提醒

- 请遵守微信使用条款
- 注意保护用户隐私
- 避免发送垃圾信息
- 合理设置回复频率

### 技术限制

- 需要微信客户端保持登录状态
- 受微信反自动化检测影响
- API服务需要保持可用
- Windows系统权限要求

### 故障排除

1. **服务启动失败**
   - 检查微信客户端是否登录
   - 验证API服务是否可用
   - 查看日志文件获取详细错误

2. **消息无法回复**
   - 检查速率限制设置
   - 验证监听目标配置
   - 确认API响应正常

3. **配置问题**
   - 使用 `python run.py config` 验证配置
   - 检查JSON格式是否正确
   - 确认文件路径权限

## 开发说明

### 代码结构

#### 配置模块 (app/config/)
- `settings.py`: 配置数据结构定义
- `config_manager.py`: 配置管理器，支持动态加载和验证
- `config.json`: 系统配置文件

#### 服务模块 (app/services/)
- `api_service.py`: API客户端，包含重试和错误处理
- `message_service.py`: 消息处理服务，整合核心功能
- `wxauto_service.py`: 微信自动化服务，统一服务接口

#### 核心功能模块 (app/core/)
- `message_processor.py`: 消息处理器，负责消息解析和过滤
- `reply_generator.py`: 回复生成器，处理回复内容和策略
- `rate_limiter.py`: 速率限制器，控制回复频率

#### 工具模块 (app/utils/)
- `logger.py`: 日志工具，统一日志管理
- `helpers.py`: 辅助工具，提供通用功能

#### 界面模块
- `main.py`: 命令行界面和交互式管理

### 扩展开发

系统采用模块化设计，可以轻松扩展：

- 添加新的消息类型处理
- 集成其他AI服务
- 扩展配置选项
- 添加Web管理界面

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

## 支持

如有问题或建议，请查看日志文件或联系开发团队。

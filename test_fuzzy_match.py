#!/usr/bin/env python3
"""
模糊匹配测试脚本
测试wxauto的模糊匹配功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from wxauto import WeChat

def test_fuzzy_match():
    """测试模糊匹配功能"""
    print("🧪 测试wxauto模糊匹配功能")
    print("="*50)
    
    try:
        # 初始化微信
        print("📱 初始化微信...")
        wx = WeChat()
        print("✅ 微信初始化成功")
        
        # 定义消息回调函数
        def message_callback(msg, chat):
            print(f"\n🎉 收到消息!")
            print(f"   发送者: {msg.sender}")
            print(f"   内容: {msg.content}")
            print(f"   类型: {msg.type}")
            print(f"   属性: {msg.attr}")
            
            # 获取聊天信息
            try:
                chat_info = msg.chat_info()
                chat_name = chat_info.get('chat_name', 'Unknown')
                chat_type = chat_info.get('chat_type', 'Unknown')
                print(f"   聊天名称: {chat_name}")
                print(f"   聊天类型: {chat_type}")
                
                # 检查是否为群聊
                if chat_type == 'group':
                    print(f"   ⚠️ 这是群聊消息，应该被过滤")
                elif chat_type == 'friend':
                    print(f"   ✅ 这是好友消息，可以处理")
                    
            except Exception as e:
                print(f"   ⚠️ 获取聊天信息失败: {str(e)}")
        
        # 使用模糊匹配添加监听
        target_name = "冯子晋"  # 使用部分名称进行模糊匹配
        print(f"\n📡 使用模糊匹配添加监听: {target_name}")
        
        try:
            result = wx.AddListenChat(
                nickname=target_name,
                callback=message_callback,
                fuzzy_match=True  # 启用模糊匹配
            )
            
            if result:
                print(f"✅ 成功添加模糊匹配监听: {target_name}")
                
                # 启动监听
                print(f"\n🚀 启动监听...")
                wx.StartListening()
                
                print("💡 测试说明:")
                print("   1. 模糊匹配已启用，会匹配包含'冯子晋'的联系人")
                print("   2. 只会处理好友消息，不会处理群聊消息")
                print("   3. 请向包含'冯子晋'的联系人发送消息")
                print("   4. 按 Ctrl+C 停止测试")
                print("\n⏰ 开始监听...")
                
                # 保持监听状态
                try:
                    start_time = time.time()
                    while True:
                        elapsed = int(time.time() - start_time)
                        print(f"\r⏰ 监听中... {elapsed//60:02d}:{elapsed%60:02d}", end="", flush=True)
                        time.sleep(1)
                except KeyboardInterrupt:
                    print(f"\n🛑 停止监听...")
                    wx.StopListening()
                    wx.RemoveListenChat(nickname=target_name)
                    print("✅ 测试结束")
                    
            else:
                print(f"❌ 添加模糊匹配监听失败: {target_name}")
                print("💡 可能的原因:")
                print("   1. 没有包含该名称的联系人")
                print("   2. 联系人不在最近聊天列表中")
                print("   3. 需要先手动打开聊天窗口")
                
        except Exception as e:
            print(f"❌ 监听设置失败: {str(e)}")
            
    except Exception as e:
        print(f"❌ 微信初始化失败: {str(e)}")
        print("💡 请确保微信客户端已启动并登录")

def test_exact_vs_fuzzy():
    """对比精确匹配和模糊匹配"""
    print("🔍 对比精确匹配和模糊匹配")
    print("="*50)
    
    try:
        wx = WeChat()
        
        # 测试名称列表
        test_names = [
            "冯子晋1568512996",  # 完整名称
            "冯子晋",            # 部分名称
            "子晋",              # 更短的部分
        ]
        
        def test_callback(msg, chat):
            chat_info = msg.chat_info()
            print(f"📨 匹配到: {chat_info.get('chat_name')} - {msg.sender}: {msg.content[:30]}...")
        
        print("🧪 测试不同匹配方式:")
        
        for name in test_names:
            print(f"\n测试名称: {name}")
            
            # 测试精确匹配
            try:
                result1 = wx.AddListenChat(nickname=name, callback=test_callback, fuzzy_match=False)
                print(f"   精确匹配: {'✅ 成功' if result1 else '❌ 失败'}")
                if result1:
                    wx.RemoveListenChat(nickname=name)
            except Exception as e:
                print(f"   精确匹配: ❌ 异常 - {str(e)}")
            
            # 测试模糊匹配
            try:
                result2 = wx.AddListenChat(nickname=name, callback=test_callback, fuzzy_match=True)
                print(f"   模糊匹配: {'✅ 成功' if result2 else '❌ 失败'}")
                if result2:
                    wx.RemoveListenChat(nickname=name)
            except Exception as e:
                print(f"   模糊匹配: ❌ 异常 - {str(e)}")
        
        print(f"\n💡 建议使用模糊匹配，匹配成功率更高")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def main():
    """主函数"""
    print("🧪 wxauto模糊匹配测试工具")
    print("="*50)
    
    while True:
        print("\n请选择测试:")
        print("1. 测试模糊匹配监听")
        print("2. 对比精确匹配和模糊匹配")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-2): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            test_fuzzy_match()
        elif choice == '2':
            test_exact_vs_fuzzy()
        else:
            print("❌ 无效选择")
        
        input("\n按回车键继续...")

if __name__ == '__main__':
    main()

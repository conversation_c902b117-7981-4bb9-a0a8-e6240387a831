# 微信自动回复系统 - 本地测试说明

## 🎯 测试目标

本次测试限制只回复微信名为 **"冯子晋1568512996"** 的联系人消息，用于验证系统功能。

## 📋 测试前准备

### 1. 环境检查
- ✅ Python 3.8+ 已安装
- ✅ 微信客户端已安装并登录
- ✅ API服务正常运行 (http://***********:9000)
- ✅ 网络连接正常

### 2. 依赖安装
```bash
pip install -r requirements.txt
```

### 3. 配置确认
当前配置已设置为只监听 "冯子晋1568512996"：
- 监听目标：冯子晋1568512996 (friend)
- 自动回复：启用
- 回复延时：0.5-2.0秒
- 速率限制：5条/分钟

## 🚀 测试步骤

### 方法一：使用测试脚本（推荐）

1. **启动测试脚本**
   ```bash
   python test_local.py
   ```
   或双击 `start_test.bat`

2. **选择测试选项**
   - 选择 "1" 测试API连接
   - 选择 "2" 检查配置
   - 选择 "3" 启动测试服务

3. **进行消息测试**
   - 使用另一个微信号向 "冯子晋1568512996" 发送消息
   - 观察系统是否自动回复
   - 查看控制台输出的实时日志

### 方法二：直接启动服务

1. **启动服务**
   ```bash
   python run.py start
   ```

2. **观察日志**
   - 控制台会显示服务状态
   - 详细日志保存在 `logs/wxauto_ml.log`

## 🔍 测试验证

### API服务测试
```bash
python test_api.py
```

这将测试：
- API健康状态检查
- 单次查询功能
- 多次查询稳定性

### 预期行为

1. **消息接收**
   - 系统检测到发给 "冯子晋1568512996" 的消息
   - 控制台显示：`📨 [测试消息] 来自 发送者: 消息内容`

2. **API调用**
   - 系统将消息发送到API服务
   - 获取智能回复内容

3. **自动回复**
   - 系统自动发送回复消息
   - 控制台显示：`💬 [自动回复] 发送给 冯子晋1568512996: 回复内容`

4. **速率控制**
   - 每分钟最多回复5条消息
   - 超过限制时会跳过回复

## 📊 测试监控

### 实时状态显示
测试脚本会显示：
- ⏰ 运行时间
- 🟢/🔴 API状态
- 📊 监听目标数量
- 📈 已处理消息数量

### 日志文件
详细日志保存在：
- `logs/wxauto_ml.log` - 主日志文件
- 包含所有消息处理、API调用、错误信息

## 🛠️ 测试配置调整

在测试脚本中选择 "5. 修改测试配置" 可以调整：

1. **回复延时**
   - 最小延时：模拟打字开始时间
   - 最大延时：模拟打字结束时间

2. **速率限制**
   - 每分钟最大回复数：防止频繁回复

3. **打字模拟**
   - 启用/禁用打字延时效果

4. **API地址**
   - 修改API服务地址

## ⚠️ 注意事项

### 安全提醒
- 仅在测试环境使用
- 避免发送敏感信息
- 注意消息发送频率
- 遵守微信使用条款

### 常见问题

1. **服务启动失败**
   - 检查微信客户端是否登录
   - 确认API服务是否运行
   - 查看错误日志

2. **无法接收消息**
   - 确认联系人名称是否正确
   - 检查监听目标配置
   - 验证微信窗口是否活跃

3. **API调用失败**
   - 测试API连接：`python test_api.py`
   - 检查网络连接
   - 确认API服务状态

4. **回复不及时**
   - 检查回复延时设置
   - 确认速率限制配置
   - 查看系统资源使用

## 🔧 故障排除

### 调试步骤
1. 运行API测试：`python test_api.py`
2. 检查配置：选择测试菜单中的 "2"
3. 查看详细日志：`logs/wxauto_ml.log`
4. 重启微信客户端
5. 重启API服务

### 日志分析
关键日志信息：
- `收到消息` - 消息接收成功
- `API调用失败` - API服务问题
- `达到速率限制` - 回复频率过高
- `已回复` - 回复发送成功

## 📈 测试结果记录

建议记录以下测试数据：
- 消息接收成功率
- API响应时间
- 回复发送成功率
- 系统稳定性
- 错误类型和频率

## 🎉 测试完成

测试完成后：
1. 按 Ctrl+C 停止服务
2. 检查日志文件
3. 分析测试结果
4. 记录问题和改进建议

---

**测试联系人**: 冯子晋1568512996  
**测试时间**: 请记录实际测试时间  
**测试环境**: Windows + Python 3.8+ + 微信客户端

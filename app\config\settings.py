"""
配置数据结构定义
定义系统各个模块的配置数据类
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class APIConfig:
    """API配置类"""
    base_url: str = "http://172.16.2.72:9000"
    endpoint: str = "/api/v1/query"
    timeout: int = 10
    max_retries: int = 3
    retry_delay: float = 1.0


@dataclass
class ReplyConfig:
    """回复策略配置类"""
    min_delay: float = 1.0
    max_delay: float = 3.0
    enable_typing_simulation: bool = True
    max_reply_length: int = 1000
    rate_limit_seconds: int = 60
    max_replies_per_minute: int = 10


@dataclass
class ListenTarget:
    """监听目标配置类"""
    name: str
    enabled: bool = True
    auto_reply: bool = True
    chat_type: str = "friend"  # friend, group
    custom_prompt: Optional[str] = None


@dataclass
class LogConfig:
    """日志配置类"""
    level: str = "INFO"
    file_path: str = "logs/wxauto_ml.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    enable_console: bool = True


@dataclass
class SystemConfig:
    """系统配置类"""
    version: str = "1.0.0"
    debug: bool = False
    auto_start: bool = False
    check_update: bool = True

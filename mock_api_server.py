#!/usr/bin/env python3
"""
模拟API服务器
用于本地测试微信自动回复系统
"""

import json
import time
import random
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading


class MockAPIHandler(BaseHTTPRequestHandler):
    """模拟API处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/api/v1/status':
            # 健康检查端点
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            response = {
                'status': 'ok',
                'message': 'Mock API Server is running',
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        else:
            self.send_error(404, 'Not Found')
    
    def do_POST(self):
        """处理POST请求"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/api/v1/query':
            # 查询端点
            try:
                # 读取请求数据
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                request_data = json.loads(post_data.decode('utf-8'))
                
                # 模拟处理时间
                processing_time = random.uniform(0.5, 2.0)
                time.sleep(processing_time)
                
                # 生成模拟回复
                query = request_data.get('query', '')
                reply = self.generate_mock_reply(query)
                
                # 构建响应
                response = {
                    'answer': reply,
                    'sources': [
                        {
                            'title': '模拟知识库',
                            'content': '这是模拟的知识库内容',
                            'score': 0.95
                        }
                    ],
                    'processing_time': processing_time,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # 发送响应
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                
                # 记录请求
                print(f"[{time.strftime('%H:%M:%S')}] 收到查询: {query[:50]}...")
                print(f"[{time.strftime('%H:%M:%S')}] 回复内容: {reply[:50]}...")
                
            except Exception as e:
                # 错误响应
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                
                error_response = {
                    'error': str(e),
                    'message': '服务器内部错误'
                }
                
                self.wfile.write(json.dumps(error_response, ensure_ascii=False).encode('utf-8'))
                print(f"[{time.strftime('%H:%M:%S')}] 处理错误: {str(e)}")
        else:
            self.send_error(404, 'Not Found')
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def generate_mock_reply(self, query):
        """生成模拟回复"""
        query_lower = query.lower().strip()
        
        # 问候语回复
        if any(word in query_lower for word in ['你好', 'hello', 'hi', '您好']):
            replies = [
                '你好！我是智能助手，很高兴为您服务！',
                'Hello！有什么可以帮助您的吗？',
                '您好！请问有什么需要帮助的？'
            ]
            return random.choice(replies)
        
        # 天气相关
        elif any(word in query_lower for word in ['天气', 'weather', '温度']):
            replies = [
                '今天天气不错，阳光明媚，温度适宜，适合外出活动。',
                '根据最新天气预报，今天多云转晴，气温18-25度。',
                '天气很好呢！记得多喝水，注意防晒哦～'
            ]
            return random.choice(replies)
        
        # 时间相关
        elif any(word in query_lower for word in ['时间', 'time', '几点', '现在']):
            current_time = time.strftime('%Y年%m月%d日 %H:%M:%S')
            return f'现在时间是：{current_time}'
        
        # 介绍相关
        elif any(word in query_lower for word in ['介绍', '你是谁', 'who are you', '自己']):
            return '我是基于wxauto开发的智能微信助手，可以自动回复消息，为您提供各种帮助和服务。'
        
        # 感谢相关
        elif any(word in query_lower for word in ['谢谢', 'thank', '感谢']):
            replies = [
                '不客气！很高兴能帮助到您！',
                '不用谢，这是我应该做的～',
                '能为您服务是我的荣幸！'
            ]
            return random.choice(replies)
        
        # 再见相关
        elif any(word in query_lower for word in ['再见', 'bye', 'goodbye', '拜拜']):
            replies = [
                '再见！祝您生活愉快！',
                '拜拜～有需要随时找我哦！',
                'Goodbye！期待下次为您服务！'
            ]
            return random.choice(replies)
        
        # 默认回复
        else:
            replies = [
                f'您说的"{query}"很有意思，让我想想怎么回答您。',
                f'关于"{query}"这个问题，我需要查询一下相关信息。',
                f'感谢您的提问！关于"{query}"，我会尽力为您解答。',
                '这是一个很好的问题，让我为您详细解答一下。',
                '我理解您的意思，这确实是一个值得思考的问题。'
            ]
            return random.choice(replies)
    
    def log_message(self, format, *args):
        """重写日志方法，减少输出"""
        pass


def start_mock_server(host='localhost', port=9000):
    """启动模拟服务器"""
    server_address = (host, port)
    httpd = HTTPServer(server_address, MockAPIHandler)
    
    print(f"🚀 模拟API服务器启动")
    print(f"📍 地址: http://{host}:{port}")
    print(f"🔗 健康检查: http://{host}:{port}/api/v1/status")
    print(f"🔗 查询端点: http://{host}:{port}/api/v1/query")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务器...")
        httpd.shutdown()
        print("✅ 服务器已停止")


def start_server_thread(host='localhost', port=9000):
    """在后台线程启动服务器"""
    server_thread = threading.Thread(
        target=start_mock_server, 
        args=(host, port),
        daemon=True
    )
    server_thread.start()
    return server_thread


if __name__ == '__main__':
    import sys
    
    # 解析命令行参数
    host = 'localhost'
    port = 9000
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--help':
            print("模拟API服务器")
            print("用法: python mock_api_server.py [host] [port]")
            print("默认: localhost 9000")
            sys.exit(0)
        else:
            host = sys.argv[1]
    
    if len(sys.argv) > 2:
        try:
            port = int(sys.argv[2])
        except ValueError:
            print("错误: 端口必须是数字")
            sys.exit(1)
    
    start_mock_server(host, port)

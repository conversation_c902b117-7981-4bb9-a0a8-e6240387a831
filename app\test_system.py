"""
系统测试模块
用于测试各个组件的功能
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config import config
from app.services import api_client, QueryRequest, message_handler


def test_config():
    """测试配置管理"""
    print("=" * 50)
    print("测试配置管理")
    print("=" * 50)
    
    # 验证配置
    errors = config.validate_config()
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    else:
        print("✅ 配置验证通过")
    
    # 显示配置信息
    print(f"API地址: {config.get_api_url()}")
    print(f"监听目标数量: {len(config.listen_targets)}")
    
    for target in config.listen_targets:
        status = "启用" if target.enabled else "禁用"
        print(f"  - {target.name} ({target.chat_type}) [{status}]")
    
    return True


def test_api_client():
    """测试API客户端"""
    print("\n" + "=" * 50)
    print("测试API客户端")
    print("=" * 50)
    
    # 健康检查
    print("执行健康检查...")
    if api_client.health_check():
        print("✅ API服务健康检查通过")
    else:
        print("❌ API服务健康检查失败")
        return False
    
    # 测试查询
    print("测试API查询...")
    request = QueryRequest(
        message="你好，这是一个测试消息",
        sender="测试用户",
        chat_type="friend",
        chat_name="测试聊天",
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )
    
    response = api_client.query(request)
    
    if response.success:
        print("✅ API查询成功")
        print(f"回复内容: {response.reply[:100]}...")
        print(f"处理时间: {response.processing_time:.2f}秒")
        return True
    else:
        print("❌ API查询失败")
        print(f"错误信息: {response.error_message}")
        return False


def test_message_handler():
    """测试消息处理器"""
    print("\n" + "=" * 50)
    print("测试消息处理器")
    print("=" * 50)
    
    # 创建模拟消息对象
    class MockMessage:
        def __init__(self, content, sender="测试用户"):
            self.content = content
            self.sender = sender
            self.id = f"test_{int(time.time())}"
            self.attr = "friend"
            self.type = "text"
        
        def chat_info(self):
            return {
                'chat_name': '文件传输助手',
                'chat_type': 'friend'
            }
        
        def quote(self, reply_content):
            print(f"模拟回复: {reply_content}")
            return type('Result', (), {'success': True})()
    
    class MockChat:
        def SendMsg(self, content):
            print(f"模拟发送消息: {content}")
            return type('Result', (), {'success': True})()
    
    # 测试消息处理
    mock_msg = MockMessage("你好，请问今天天气如何？")
    mock_chat = MockChat()
    
    print("处理测试消息...")
    try:
        message_handler.handle_message(mock_msg, mock_chat)
        print("✅ 消息处理测试完成")
        return True
    except Exception as e:
        print(f"❌ 消息处理测试失败: {str(e)}")
        return False


def test_rate_limiter():
    """测试速率限制器"""
    print("\n" + "=" * 50)
    print("测试速率限制器")
    print("=" * 50)
    
    from app.message_handler import RateLimiter
    
    rate_limiter = RateLimiter()
    chat_name = "测试聊天"
    
    # 测试正常情况
    if rate_limiter.can_reply(chat_name):
        print("✅ 初始状态可以回复")
        rate_limiter.record_reply(chat_name)
    else:
        print("❌ 初始状态不能回复")
        return False
    
    # 测试速率限制
    max_replies = config.reply_config.max_replies_per_minute
    for i in range(max_replies):
        rate_limiter.record_reply(chat_name)
    
    if not rate_limiter.can_reply(chat_name):
        print("✅ 速率限制生效")
        return True
    else:
        print("❌ 速率限制未生效")
        return False


def run_all_tests():
    """运行所有测试"""
    print("微信自动回复系统 - 功能测试")
    print("=" * 50)
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    tests = [
        ("配置管理", test_config),
        ("API客户端", test_api_client),
        ("消息处理器", test_message_handler),
        ("速率限制器", test_rate_limiter),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常运行")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和环境")
        return False


if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)

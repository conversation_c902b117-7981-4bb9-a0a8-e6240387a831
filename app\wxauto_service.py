"""
微信自动回复服务主模块
整合所有功能模块，提供统一的服务接口
"""

import logging
import time
import threading
from typing import Dict, List, Optional
from pathlib import Path
from wxauto import WeChat
from .config import config
from .api_client import api_client
from .message_handler import message_handler


class WxAutoService:
    """微信自动回复服务类"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        self.wx: Optional[WeChat] = None
        self.is_running = False
        self.listen_chats: Dict[str, any] = {}
        self.service_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('wxauto_ml')
        logger.setLevel(getattr(logging, config.log_config.level))
        
        # 清除现有的处理器
        logger.handlers.clear()
        
        # 创建日志目录
        log_path = Path(config.log_config.file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 文件处理器
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            log_path,
            maxBytes=config.log_config.max_file_size,
            backupCount=config.log_config.backup_count,
            encoding='utf-8'
        )
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # 控制台处理器
        if config.log_config.enable_console:
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def start(self) -> bool:
        """
        启动微信自动回复服务
        
        Returns:
            bool: 启动是否成功
        """
        if self.is_running:
            self.logger.warning("服务已经在运行中")
            return True
        
        try:
            # 验证配置
            config_errors = config.validate_config()
            if config_errors:
                self.logger.error(f"配置验证失败: {', '.join(config_errors)}")
                return False
            
            # 检查API服务健康状态
            if not api_client.health_check():
                self.logger.error("API服务不可用，请检查服务状态")
                return False
            
            # 初始化微信客户端
            self.logger.info("正在初始化微信客户端...")
            self.wx = WeChat()
            
            # 添加监听目标
            enabled_targets = config.get_enabled_targets()
            if not enabled_targets:
                self.logger.error("没有启用的监听目标")
                return False
            
            success_count = 0
            for target in enabled_targets:
                if self._add_listen_target(target):
                    success_count += 1
            
            if success_count == 0:
                self.logger.error("没有成功添加任何监听目标")
                return False
            
            # 启动监听
            self.wx.StartListening()
            self.is_running = True
            
            self.logger.info(f"微信自动回复服务启动成功，监听 {success_count} 个目标")
            
            # 启动服务监控线程
            self.service_thread = threading.Thread(target=self._service_monitor, daemon=True)
            self.service_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动服务时发生错误: {str(e)}", exc_info=True)
            return False
    
    def stop(self):
        """停止微信自动回复服务"""
        if not self.is_running:
            self.logger.warning("服务未在运行")
            return
        
        try:
            self.logger.info("正在停止微信自动回复服务...")
            
            # 设置停止标志
            self._stop_event.set()
            self.is_running = False
            
            # 停止微信监听
            if self.wx:
                self.wx.StopListening()
                
                # 移除所有监听
                for target_name in list(self.listen_chats.keys()):
                    self._remove_listen_target(target_name)
            
            # 关闭API客户端
            api_client.close()
            
            self.logger.info("微信自动回复服务已停止")
            
        except Exception as e:
            self.logger.error(f"停止服务时发生错误: {str(e)}", exc_info=True)
    
    def _add_listen_target(self, target) -> bool:
        """添加监听目标"""
        try:
            self.logger.info(f"添加监听目标: {target.name}")
            
            result = self.wx.AddListenChat(
                nickname=target.name,
                callback=message_handler.handle_message
            )
            
            if hasattr(result, 'success') and not result.success:
                self.logger.error(f"添加监听失败: {target.name} - {getattr(result, 'message', '未知错误')}")
                return False
            
            self.listen_chats[target.name] = result
            self.logger.info(f"成功添加监听目标: {target.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加监听目标失败: {target.name} - {str(e)}")
            return False
    
    def _remove_listen_target(self, target_name: str):
        """移除监听目标"""
        try:
            if target_name in self.listen_chats:
                self.wx.RemoveListenChat(nickname=target_name)
                del self.listen_chats[target_name]
                self.logger.info(f"移除监听目标: {target_name}")
        except Exception as e:
            self.logger.error(f"移除监听目标失败: {target_name} - {str(e)}")
    
    def _service_monitor(self):
        """服务监控线程"""
        while not self._stop_event.is_set():
            try:
                # 检查API服务健康状态
                if not api_client.health_check():
                    self.logger.warning("API服务健康检查失败")
                
                # 等待下次检查
                self._stop_event.wait(60)  # 每分钟检查一次
                
            except Exception as e:
                self.logger.error(f"服务监控异常: {str(e)}")
                self._stop_event.wait(10)
    
    def reload_config(self) -> bool:
        """重新加载配置"""
        try:
            self.logger.info("重新加载配置...")
            
            # 重新加载配置文件
            config._load_config()
            
            # 验证新配置
            config_errors = config.validate_config()
            if config_errors:
                self.logger.error(f"新配置验证失败: {', '.join(config_errors)}")
                return False
            
            # 如果服务正在运行，需要重新设置监听目标
            if self.is_running and self.wx:
                # 获取当前启用的目标
                enabled_targets = {t.name for t in config.get_enabled_targets()}
                current_targets = set(self.listen_chats.keys())
                
                # 移除不再需要的目标
                for target_name in current_targets - enabled_targets:
                    self._remove_listen_target(target_name)
                
                # 添加新的目标
                for target in config.get_enabled_targets():
                    if target.name not in self.listen_chats:
                        self._add_listen_target(target)
            
            self.logger.info("配置重新加载完成")
            return True
            
        except Exception as e:
            self.logger.error(f"重新加载配置失败: {str(e)}")
            return False
    
    def get_status(self) -> Dict:
        """获取服务状态"""
        return {
            'is_running': self.is_running,
            'listen_targets': list(self.listen_chats.keys()),
            'api_health': api_client.health_check(),
            'message_handler_stats': message_handler.get_stats(),
            'config_valid': len(config.validate_config()) == 0
        }
    
    def keep_running(self):
        """保持服务运行"""
        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("接收到中断信号，正在停止服务...")
            self.stop()


# 全局服务实例
wx_service = WxAutoService()

"""
辅助工具模块
提供各种通用的辅助函数
"""

import json
import time
import functools
from datetime import datetime
from typing import Any, Optional, Callable, Dict


def format_timestamp(timestamp: Optional[datetime] = None, 
                    format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化时间戳
    
    Args:
        timestamp: 时间戳对象，为空则使用当前时间
        format_str: 格式化字符串
        
    Returns:
        str: 格式化后的时间字符串
    """
    if timestamp is None:
        timestamp = datetime.now()
    
    return timestamp.strftime(format_str)


def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    截断字符串
    
    Args:
        text: 原始字符串
        max_length: 最大长度
        suffix: 截断后缀
        
    Returns:
        str: 截断后的字符串
    """
    if not text:
        return ""
    
    # 去除换行符和多余空格
    text = text.replace('\n', ' ').replace('\r', ' ').strip()
    text = ' '.join(text.split())
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """
    安全的JSON解析
    
    Args:
        json_str: JSON字符串
        default: 解析失败时的默认值
        
    Returns:
        Any: 解析结果或默认值
    """
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError, ValueError):
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """
    安全的JSON序列化
    
    Args:
        obj: 要序列化的对象
        default: 序列化失败时的默认值
        
    Returns:
        str: JSON字符串或默认值
    """
    try:
        return json.dumps(obj, ensure_ascii=False, indent=2)
    except (TypeError, ValueError):
        return default


def retry_on_exception(max_retries: int = 3, 
                      delay: float = 1.0,
                      backoff_factor: float = 2.0,
                      exceptions: tuple = (Exception,)) -> Callable:
    """
    异常重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延时时间
        backoff_factor: 退避因子
        exceptions: 需要重试的异常类型
        
    Returns:
        Callable: 装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        raise last_exception
            
            return None
        
        return wrapper
    return decorator


def validate_config_dict(config_dict: Dict, required_keys: list) -> list:
    """
    验证配置字典
    
    Args:
        config_dict: 配置字典
        required_keys: 必需的键列表
        
    Returns:
        list: 错误信息列表
    """
    errors = []
    
    if not isinstance(config_dict, dict):
        errors.append("配置必须是字典类型")
        return errors
    
    for key in required_keys:
        if key not in config_dict:
            errors.append(f"缺少必需的配置项: {key}")
        elif config_dict[key] is None:
            errors.append(f"配置项不能为空: {key}")
    
    return errors


def merge_dicts(dict1: Dict, dict2: Dict) -> Dict:
    """
    合并字典
    
    Args:
        dict1: 第一个字典
        dict2: 第二个字典
        
    Returns:
        Dict: 合并后的字典
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_dicts(result[key], value)
        else:
            result[key] = value
    
    return result


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除非法字符
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 清理后的文件名
    """
    import re
    
    # 移除或替换非法字符
    illegal_chars = r'[<>:"/\\|?*]'
    filename = re.sub(illegal_chars, '_', filename)
    
    # 移除首尾空格和点
    filename = filename.strip(' .')
    
    # 限制长度
    if len(filename) > 200:
        filename = filename[:200]
    
    return filename or "unnamed"


def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    计算文本相似度（简单版本）
    
    Args:
        text1: 第一个文本
        text2: 第二个文本
        
    Returns:
        float: 相似度（0-1之间）
    """
    if not text1 or not text2:
        return 0.0
    
    # 简单的字符级相似度计算
    set1 = set(text1.lower())
    set2 = set(text2.lower())
    
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    
    return intersection / union if union > 0 else 0.0


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        str: 格式化后的大小字符串
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def is_valid_url(url: str) -> bool:
    """
    验证URL是否有效
    
    Args:
        url: URL字符串
        
    Returns:
        bool: 是否有效
    """
    import re
    
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return url_pattern.match(url) is not None


def extract_keywords(text: str, max_keywords: int = 10) -> list:
    """
    提取文本关键词（简单版本）
    
    Args:
        text: 输入文本
        max_keywords: 最大关键词数量
        
    Returns:
        list: 关键词列表
    """
    if not text:
        return []
    
    # 简单的关键词提取：按词频统计
    import re
    from collections import Counter
    
    # 移除标点符号，转换为小写
    words = re.findall(r'\b\w+\b', text.lower())
    
    # 过滤停用词（简单版本）
    stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
    
    words = [word for word in words if word not in stop_words and len(word) > 1]
    
    # 统计词频
    word_counts = Counter(words)
    
    # 返回最常见的关键词
    return [word for word, count in word_counts.most_common(max_keywords)]

## Code Style
- Do not add excessive comments within function bodies. Only add comments within function bodies to highlight specific details that may not be obvious.
- For public functions, add function documentation that summarizes the behavior of the function in 1-3 sentences and document all parameters, return values, and any errors the function might throw.
- For private functions, do not write complete function documentation and just summarize what the function does in 1-2 sentences.
## Source Code
- 所有的源代码都在".venv\Lib\site-packages\wxauto"目录下，应用都放在app\目录下。
- 本项目相关的概念都在"https://docs.wxauto.org/docs/concepts/"文档之中。
- wx.py是核心实现文件，包含 WeChat 和 Chat 类的主要功能，从文档的分支中获取具体的数据结构，请勿在生成代码的过程中重复定义数据结构
- 消息处理都在\wxauto\msgs\目录下，base.py包含了消息的基础属性和通用方法，相关的文档是"https://docs.wxauto.org/docs/class/message/"，仔细阅读请勿重复定义已有的类
- 本项目还会用到/api/v1/query作为消息的回复，从"101.34.74.190:9000"这个域获取，api的说明文档在api-reference.md，简单的引用即可
"""
速率限制器核心模块
负责控制消息回复的频率和速率
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from collections import defaultdict, deque
from ..config import config


class RateLimiter:
    """速率限制器核心类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.message_times: Dict[str, deque] = defaultdict(deque)
        self.user_message_times: Dict[str, Dict[str, deque]] = defaultdict(lambda: defaultdict(deque))
        self.global_message_times: deque = deque()
    
    def can_reply(self, chat_name: str, sender: str = None) -> bool:
        """
        检查是否可以回复
        
        Args:
            chat_name: 聊天名称
            sender: 发送者（可选）
            
        Returns:
            bool: 是否可以回复
        """
        # 检查聊天级别的速率限制
        if not self._can_reply_to_chat(chat_name):
            self.logger.debug(f"聊天级别速率限制: {chat_name}")
            return False
        
        # 检查用户级别的速率限制
        if sender and not self._can_reply_to_user(chat_name, sender):
            self.logger.debug(f"用户级别速率限制: {sender} in {chat_name}")
            return False
        
        # 检查全局速率限制
        if not self._can_reply_globally():
            self.logger.debug("全局速率限制")
            return False
        
        return True
    
    def _can_reply_to_chat(self, chat_name: str) -> bool:
        """检查聊天级别的速率限制"""
        now = datetime.now()
        cutoff_time = now - timedelta(seconds=config.reply_config.rate_limit_seconds)
        
        # 清理过期的时间戳
        times = self.message_times[chat_name]
        while times and times[0] < cutoff_time:
            times.popleft()
        
        # 检查是否超过限制
        return len(times) < config.reply_config.max_replies_per_minute
    
    def _can_reply_to_user(self, chat_name: str, sender: str) -> bool:
        """检查用户级别的速率限制"""
        now = datetime.now()
        cutoff_time = now - timedelta(seconds=config.reply_config.rate_limit_seconds)
        
        # 清理过期的时间戳
        times = self.user_message_times[chat_name][sender]
        while times and times[0] < cutoff_time:
            times.popleft()
        
        # 用户级别限制：每分钟最多回复5次
        max_user_replies = min(5, config.reply_config.max_replies_per_minute)
        return len(times) < max_user_replies
    
    def _can_reply_globally(self) -> bool:
        """检查全局速率限制"""
        now = datetime.now()
        cutoff_time = now - timedelta(seconds=60)  # 全局限制时间窗口为1分钟
        
        # 清理过期的时间戳
        while self.global_message_times and self.global_message_times[0] < cutoff_time:
            self.global_message_times.popleft()
        
        # 全局限制：每分钟最多回复50次
        global_limit = 50
        return len(self.global_message_times) < global_limit
    
    def record_reply(self, chat_name: str, sender: str = None):
        """
        记录回复时间
        
        Args:
            chat_name: 聊天名称
            sender: 发送者（可选）
        """
        now = datetime.now()
        
        # 记录聊天级别的回复时间
        self.message_times[chat_name].append(now)
        
        # 记录用户级别的回复时间
        if sender:
            self.user_message_times[chat_name][sender].append(now)
        
        # 记录全局回复时间
        self.global_message_times.append(now)
        
        self.logger.debug(f"记录回复时间: {chat_name} - {sender}")
    
    def get_wait_time(self, chat_name: str, sender: str = None) -> float:
        """
        获取需要等待的时间
        
        Args:
            chat_name: 聊天名称
            sender: 发送者（可选）
            
        Returns:
            float: 等待时间（秒）
        """
        if self.can_reply(chat_name, sender):
            return 0.0
        
        wait_times = []
        
        # 计算聊天级别的等待时间
        chat_wait = self._get_chat_wait_time(chat_name)
        if chat_wait > 0:
            wait_times.append(chat_wait)
        
        # 计算用户级别的等待时间
        if sender:
            user_wait = self._get_user_wait_time(chat_name, sender)
            if user_wait > 0:
                wait_times.append(user_wait)
        
        # 计算全局等待时间
        global_wait = self._get_global_wait_time()
        if global_wait > 0:
            wait_times.append(global_wait)
        
        return max(wait_times) if wait_times else 0.0
    
    def _get_chat_wait_time(self, chat_name: str) -> float:
        """获取聊天级别的等待时间"""
        times = self.message_times[chat_name]
        if not times:
            return 0.0
        
        now = datetime.now()
        rate_limit_seconds = config.reply_config.rate_limit_seconds
        oldest_allowed_time = now - timedelta(seconds=rate_limit_seconds)
        
        if len(times) >= config.reply_config.max_replies_per_minute:
            # 需要等到最早的消息过期
            wait_until = times[0] + timedelta(seconds=rate_limit_seconds)
            wait_time = (wait_until - now).total_seconds()
            return max(0, wait_time)
        
        return 0.0
    
    def _get_user_wait_time(self, chat_name: str, sender: str) -> float:
        """获取用户级别的等待时间"""
        times = self.user_message_times[chat_name][sender]
        if not times:
            return 0.0
        
        now = datetime.now()
        rate_limit_seconds = config.reply_config.rate_limit_seconds
        max_user_replies = min(5, config.reply_config.max_replies_per_minute)
        
        if len(times) >= max_user_replies:
            wait_until = times[0] + timedelta(seconds=rate_limit_seconds)
            wait_time = (wait_until - now).total_seconds()
            return max(0, wait_time)
        
        return 0.0
    
    def _get_global_wait_time(self) -> float:
        """获取全局等待时间"""
        if not self.global_message_times:
            return 0.0
        
        now = datetime.now()
        global_limit = 50
        
        if len(self.global_message_times) >= global_limit:
            wait_until = self.global_message_times[0] + timedelta(seconds=60)
            wait_time = (wait_until - now).total_seconds()
            return max(0, wait_time)
        
        return 0.0
    
    def get_stats(self) -> Dict:
        """
        获取速率限制统计信息
        
        Returns:
            Dict: 统计信息
        """
        now = datetime.now()
        cutoff_time = now - timedelta(seconds=config.reply_config.rate_limit_seconds)
        
        # 清理过期数据并统计
        chat_stats = {}
        for chat_name, times in self.message_times.items():
            # 清理过期时间戳
            while times and times[0] < cutoff_time:
                times.popleft()
            chat_stats[chat_name] = len(times)
        
        # 统计用户级别数据
        user_stats = {}
        for chat_name, users in self.user_message_times.items():
            user_stats[chat_name] = {}
            for sender, times in users.items():
                while times and times[0] < cutoff_time:
                    times.popleft()
                user_stats[chat_name][sender] = len(times)
        
        # 清理全局数据
        global_cutoff = now - timedelta(seconds=60)
        while self.global_message_times and self.global_message_times[0] < global_cutoff:
            self.global_message_times.popleft()
        
        return {
            'chat_stats': chat_stats,
            'user_stats': user_stats,
            'global_count': len(self.global_message_times),
            'rate_limit_config': {
                'max_replies_per_minute': config.reply_config.max_replies_per_minute,
                'rate_limit_seconds': config.reply_config.rate_limit_seconds
            }
        }
    
    def reset_limits(self, chat_name: str = None, sender: str = None):
        """
        重置速率限制
        
        Args:
            chat_name: 聊天名称（可选，为空则重置所有）
            sender: 发送者（可选）
        """
        if chat_name is None:
            # 重置所有限制
            self.message_times.clear()
            self.user_message_times.clear()
            self.global_message_times.clear()
            self.logger.info("已重置所有速率限制")
        elif sender is None:
            # 重置特定聊天的限制
            if chat_name in self.message_times:
                self.message_times[chat_name].clear()
            if chat_name in self.user_message_times:
                self.user_message_times[chat_name].clear()
            self.logger.info(f"已重置聊天 {chat_name} 的速率限制")
        else:
            # 重置特定用户的限制
            if chat_name in self.user_message_times and sender in self.user_message_times[chat_name]:
                self.user_message_times[chat_name][sender].clear()
            self.logger.info(f"已重置用户 {sender} 在 {chat_name} 的速率限制")


# 全局速率限制器实例
rate_limiter = RateLimiter()

@echo off
chcp 65001 >nul
title 微信自动回复系统 - 完整测试

echo ========================================
echo 微信自动回复系统 - 完整测试
echo ========================================
echo.
echo 测试目标: 冯子晋1568512996
echo API服务: 本地模拟服务器
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 激活虚拟环境（如果存在）
if exist ".venv\Scripts\activate.bat" (
    echo 激活虚拟环境...
    call .venv\Scripts\activate.bat
)

REM 检查依赖
echo 检查依赖包...
python -c "import wxauto, requests" >nul 2>&1
if errorlevel 1 (
    echo 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo 启动模拟API服务器
echo ========================================
echo.

REM 启动模拟API服务器
start "模拟API服务器" python mock_api_server.py

REM 等待服务器启动
echo 等待API服务器启动...
timeout /t 3 /nobreak >nul

REM 测试API连接
echo 测试API连接...
python test_api.py
if errorlevel 1 (
    echo.
    echo 警告: API测试失败，但仍可继续测试
    echo.
)

echo.
echo ========================================
echo 启动微信自动回复测试
echo ========================================
echo.

REM 启动测试脚本
python test_local.py

echo.
echo 测试完成！
pause

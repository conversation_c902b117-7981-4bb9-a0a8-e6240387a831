#!/usr/bin/env python3
"""
API测试脚本
快速测试API服务是否可用
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import config
from app.services import api_client, QueryRequest


def test_api_health():
    """测试API健康状态"""
    print("🔍 测试API健康状态...")
    print(f"API地址: {config.get_api_url()}")
    
    if api_client.health_check():
        print("✅ API服务健康检查通过")
        return True
    else:
        print("❌ API服务健康检查失败")
        return False


def test_api_query():
    """测试API查询功能"""
    print("\n🔍 测试API查询功能...")
    
    # 创建测试请求
    test_request = QueryRequest(
        message="你好，这是一个测试消息",
        sender="测试用户",
        chat_type="friend",
        chat_name="冯子晋**********",
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )
    
    print(f"发送测试消息: {test_request.message}")
    
    # 发送请求
    start_time = time.time()
    response = api_client.query(test_request)
    end_time = time.time()
    
    print(f"响应时间: {end_time - start_time:.2f}秒")
    
    if response.success:
        print("✅ API查询成功")
        print(f"回复内容: {response.reply}")
        if response.processing_time:
            print(f"处理时间: {response.processing_time:.2f}秒")
        return True
    else:
        print("❌ API查询失败")
        print(f"错误信息: {response.error_message}")
        return False


def test_multiple_queries():
    """测试多次查询"""
    print("\n🔍 测试多次查询...")
    
    test_messages = [
        "你好",
        "今天天气怎么样？",
        "请介绍一下你自己",
        "谢谢你的帮助",
        "再见"
    ]
    
    success_count = 0
    total_time = 0
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n测试 {i}/{len(test_messages)}: {message}")
        
        request = QueryRequest(
            message=message,
            sender="测试用户",
            chat_type="friend", 
            chat_name="冯子晋**********",
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
        start_time = time.time()
        response = api_client.query(request)
        end_time = time.time()
        
        query_time = end_time - start_time
        total_time += query_time
        
        if response.success:
            print(f"✅ 成功 ({query_time:.2f}s): {response.reply[:100]}...")
            success_count += 1
        else:
            print(f"❌ 失败 ({query_time:.2f}s): {response.error_message}")
        
        # 避免请求过快
        time.sleep(0.5)
    
    print(f"\n📊 测试结果:")
    print(f"成功率: {success_count}/{len(test_messages)} ({success_count/len(test_messages)*100:.1f}%)")
    print(f"平均响应时间: {total_time/len(test_messages):.2f}秒")
    
    return success_count == len(test_messages)


def main():
    """主函数"""
    print("🧪 API服务测试")
    print("="*50)
    
    # 测试健康状态
    if not test_api_health():
        print("\n❌ API服务不可用，请检查:")
        print("1. API服务是否启动")
        print("2. 网络连接是否正常")
        print("3. API地址是否正确")
        return 1
    
    # 测试单次查询
    if not test_api_query():
        print("\n❌ API查询功能异常")
        return 1
    
    # 询问是否进行多次测试
    choice = input("\n是否进行多次查询测试？(y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        if test_multiple_queries():
            print("\n🎉 所有测试通过！API服务工作正常")
        else:
            print("\n⚠️ 部分测试失败，请检查API服务稳定性")
    
    print("\n✅ API测试完成")
    return 0


if __name__ == '__main__':
    sys.exit(main())

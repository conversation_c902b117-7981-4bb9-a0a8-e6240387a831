#!/usr/bin/env python3
"""
调试脚本 - 检查微信联系人
用于确认联系人名称是否正确
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from wxauto import WeChat
    
    def check_contacts():
        """检查微信联系人"""
        print("🔍 正在检查微信联系人...")

        try:
            # 初始化微信
            wx = WeChat()
            print(f"✅ 微信初始化成功")

            # 尝试搜索目标联系人
            target_name = "冯子晋1568512996"
            print(f"\n🎯 搜索目标联系人: {target_name}")

            try:
                # 尝试切换到目标联系人
                chat = wx.ChatWith(target_name)
                if chat:
                    print(f"✅ 找到联系人: {target_name}")
                    print(f"📱 聊天对象类型: {type(chat)}")

                    # 尝试获取最近消息
                    try:
                        messages = chat.GetAllMessage()
                        print(f"📨 最近消息数量: {len(messages)}")

                        if messages:
                            print("📝 最近3条消息:")
                            for i, msg in enumerate(messages[-3:], 1):
                                print(f"  {i}. 发送者: {msg.sender}")
                                print(f"     内容: {msg.content[:50]}...")
                                print(f"     类型: {msg.type}")
                                print(f"     属性: {msg.attr}")
                                print()
                    except Exception as e:
                        print(f"⚠️ 获取消息失败: {str(e)}")

                else:
                    print(f"❌ 未找到联系人: {target_name}")
                    print("💡 可能的原因:")
                    print("   1. 联系人名称不完全匹配")
                    print("   2. 联系人不在最近聊天列表中")
                    print("   3. 需要先手动打开该联系人的聊天窗口")

            except Exception as e:
                print(f"❌ 搜索联系人失败: {str(e)}")

            print("\n💡 调试建议:")
            print("   1. 请在微信中手动打开与'冯子晋1568512996'的聊天窗口")
            print("   2. 确认联系人名称完全一致（包括数字、空格等）")
            print("   3. 尝试发送一条消息确保聊天处于活跃状态")

        except Exception as e:
            print(f"❌ 微信初始化失败: {str(e)}")
            print("💡 请确保:")
            print("   1. 微信客户端已启动并登录")
            print("   2. 微信窗口处于可见状态")
            print("   3. 没有其他程序占用微信")
    
    def test_message_detection():
        """测试消息检测"""
        print("\n🧪 测试消息检测功能...")
        
        try:
            wx = WeChat()
            target_name = "冯子晋1568512996"
            
            # 尝试添加监听
            print(f"📡 尝试添加监听: {target_name}")
            
            def test_callback(msg, chat):
                print(f"🎉 检测到消息!")
                print(f"   发送者: {msg.sender}")
                print(f"   内容: {msg.content}")
                print(f"   聊天: {chat}")
            
            # 添加监听
            result = wx.AddListenChat(nickname=target_name, callback=test_callback)
            print(f"📡 监听添加结果: {result}")
            
            if result:
                print("✅ 监听添加成功")
                print("💡 现在请向该联系人发送一条测试消息...")
                print("⏰ 等待30秒...")
                
                import time
                for i in range(30, 0, -1):
                    print(f"\r⏰ 倒计时: {i:2d}秒", end="", flush=True)
                    time.sleep(1)
                
                print("\n⏹️ 测试结束")
                wx.RemoveListenChat(nickname=target_name)
            else:
                print("❌ 监听添加失败")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    def main():
        """主函数"""
        print("🔧 微信联系人调试工具")
        print("="*50)
        
        while True:
            print("\n请选择操作:")
            print("1. 检查联系人")
            print("2. 测试消息检测")
            print("3. 显示配置信息")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-3): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                check_contacts()
            elif choice == '2':
                test_message_detection()
            elif choice == '3':
                show_config()
            else:
                print("❌ 无效选择")
            
            input("\n按回车键继续...")
    
    def show_config():
        """显示配置信息"""
        from app.config import config
        
        print("\n📋 当前配置信息:")
        print(f"API地址: {config.get_api_url()}")
        print(f"监听目标:")
        
        for target in config.listen_targets:
            status = "启用" if target.enabled else "禁用"
            auto_reply = "自动回复" if target.auto_reply else "仅监听"
            print(f"  - {target.name} ({target.chat_type}) [{status}] [{auto_reply}]")
    
    if __name__ == '__main__':
        main()

except ImportError as e:
    print(f"❌ 导入错误: {str(e)}")
    print("💡 请确保已安装wxauto: pip install wxauto")
except Exception as e:
    print(f"❌ 未知错误: {str(e)}")

@echo off
chcp 65001 >nul
title 微信自动回复系统 - 本地测试

echo ========================================
echo 微信自动回复系统 - 本地测试模式
echo ========================================
echo.
echo 测试目标: 冯子晋1568512996
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 激活虚拟环境（如果存在）
if exist ".venv\Scripts\activate.bat" (
    echo 激活虚拟环境...
    call .venv\Scripts\activate.bat
)

REM 检查依赖
echo 检查依赖包...
python -c "import wxauto, requests" >nul 2>&1
if errorlevel 1 (
    echo 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 启动测试
echo.
echo 启动本地测试...
echo.
python test_local.py

pause

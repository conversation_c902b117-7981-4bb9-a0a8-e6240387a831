"""
微信自动回复系统主入口
提供命令行界面和服务管理功能
"""

import sys
import argparse
import signal
from pathlib import Path
from .services import wx_service
from .config import config


def signal_handler(signum, frame):
    """信号处理器"""
    print("\n接收到停止信号，正在关闭服务...")
    wx_service.stop()
    sys.exit(0)


def start_service():
    """启动服务"""
    print("=" * 50)
    print("微信自动回复系统 v1.0")
    print("=" * 50)
    
    # 显示配置信息
    print(f"API服务地址: {config.get_api_url()}")
    print(f"监听目标数量: {len(config.get_enabled_targets())}")
    
    enabled_targets = config.get_enabled_targets()
    if enabled_targets:
        print("启用的监听目标:")
        for target in enabled_targets:
            status = "启用" if target.enabled else "禁用"
            auto_reply = "自动回复" if target.auto_reply else "仅监听"
            print(f"  - {target.name} ({target.chat_type}) [{status}] [{auto_reply}]")
    
    print("-" * 50)
    
    # 启动服务
    if wx_service.start():
        print("服务启动成功！")
        print("按 Ctrl+C 停止服务")
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 保持运行
        wx_service.keep_running()
    else:
        print("服务启动失败，请检查日志")
        return 1
    
    return 0


def show_status():
    """显示服务状态"""
    status = wx_service.get_status()
    
    print("=" * 50)
    print("服务状态")
    print("=" * 50)
    print(f"运行状态: {'运行中' if status['is_running'] else '已停止'}")
    print(f"API健康状态: {'正常' if status['api_health'] else '异常'}")
    print(f"配置状态: {'有效' if status['config_valid'] else '无效'}")
    
    if status['listen_targets']:
        print(f"监听目标: {', '.join(status['listen_targets'])}")
    else:
        print("监听目标: 无")
    
    # 显示消息处理统计
    stats = status['message_handler_stats']
    print(f"已处理消息数: {stats['processed_messages_count']}")
    
    if stats['rate_limiter_status']:
        print("速率限制状态:")
        for chat, count in stats['rate_limiter_status'].items():
            print(f"  - {chat}: {count} 条/分钟")


def manage_config():
    """配置管理"""
    print("=" * 50)
    print("配置管理")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 查看当前配置")
        print("2. 添加监听目标")
        print("3. 移除监听目标")
        print("4. 修改目标状态")
        print("5. 验证配置")
        print("0. 返回主菜单")
        
        choice = input("请输入选择 (0-5): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            _show_config()
        elif choice == '2':
            _add_target()
        elif choice == '3':
            _remove_target()
        elif choice == '4':
            _modify_target()
        elif choice == '5':
            _validate_config()
        else:
            print("无效选择，请重试")


def _show_config():
    """显示当前配置"""
    print("\n当前配置:")
    print(f"API地址: {config.get_api_url()}")
    print(f"请求超时: {config.api_config.timeout}秒")
    print(f"最大重试: {config.api_config.max_retries}次")
    print(f"回复延时: {config.reply_config.min_delay}-{config.reply_config.max_delay}秒")
    
    print("\n监听目标:")
    for target in config.listen_targets:
        status = "启用" if target.enabled else "禁用"
        auto_reply = "自动回复" if target.auto_reply else "仅监听"
        print(f"  - {target.name} ({target.chat_type}) [{status}] [{auto_reply}]")


def _add_target():
    """添加监听目标"""
    print("\n添加监听目标:")
    name = input("目标名称: ").strip()
    if not name:
        print("名称不能为空")
        return
    
    # 检查是否已存在
    if config.get_listen_target(name):
        print("目标已存在")
        return
    
    chat_type = input("聊天类型 (friend/group) [friend]: ").strip() or "friend"
    if chat_type not in ['friend', 'group']:
        print("无效的聊天类型")
        return
    
    enabled = input("是否启用 (y/n) [y]: ").strip().lower() != 'n'
    auto_reply = input("是否自动回复 (y/n) [y]: ").strip().lower() != 'n'
    
    custom_prompt = input("自定义提示词 (可选): ").strip() or None
    
    config.add_listen_target(name, chat_type, enabled, auto_reply, custom_prompt)
    print(f"已添加监听目标: {name}")


def _remove_target():
    """移除监听目标"""
    if not config.listen_targets:
        print("没有配置的监听目标")
        return
    
    print("\n当前监听目标:")
    for i, target in enumerate(config.listen_targets, 1):
        print(f"{i}. {target.name}")
    
    try:
        choice = int(input("请选择要移除的目标 (序号): "))
        if 1 <= choice <= len(config.listen_targets):
            target = config.listen_targets[choice - 1]
            config.remove_listen_target(target.name)
            print(f"已移除监听目标: {target.name}")
        else:
            print("无效选择")
    except ValueError:
        print("请输入有效数字")


def _modify_target():
    """修改目标状态"""
    if not config.listen_targets:
        print("没有配置的监听目标")
        return
    
    print("\n当前监听目标:")
    for i, target in enumerate(config.listen_targets, 1):
        status = "启用" if target.enabled else "禁用"
        auto_reply = "自动回复" if target.auto_reply else "仅监听"
        print(f"{i}. {target.name} [{status}] [{auto_reply}]")
    
    try:
        choice = int(input("请选择要修改的目标 (序号): "))
        if 1 <= choice <= len(config.listen_targets):
            target = config.listen_targets[choice - 1]
            
            enabled_input = input(f"是否启用 (当前: {'是' if target.enabled else '否'}) (y/n/回车跳过): ").strip().lower()
            auto_reply_input = input(f"是否自动回复 (当前: {'是' if target.auto_reply else '否'}) (y/n/回车跳过): ").strip().lower()
            
            enabled = None if enabled_input == '' else enabled_input == 'y'
            auto_reply = None if auto_reply_input == '' else auto_reply_input == 'y'
            
            config.update_target_status(target.name, enabled, auto_reply)
            print(f"已更新目标状态: {target.name}")
        else:
            print("无效选择")
    except ValueError:
        print("请输入有效数字")


def _validate_config():
    """验证配置"""
    errors = config.validate_config()
    if errors:
        print("\n配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n配置验证通过")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='微信自动回复系统')
    parser.add_argument('command', nargs='?', choices=['start', 'status', 'config'], 
                       help='命令: start(启动服务), status(查看状态), config(配置管理)')
    
    args = parser.parse_args()
    
    if args.command == 'start':
        return start_service()
    elif args.command == 'status':
        show_status()
        return 0
    elif args.command == 'config':
        manage_config()
        return 0
    else:
        # 交互式菜单
        while True:
            print("\n" + "=" * 50)
            print("微信自动回复系统")
            print("=" * 50)
            print("1. 启动服务")
            print("2. 查看状态")
            print("3. 配置管理")
            print("0. 退出")
            
            choice = input("请选择操作 (0-3): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                start_service()
            elif choice == '2':
                show_status()
            elif choice == '3':
                manage_config()
            else:
                print("无效选择，请重试")
    
    return 0


if __name__ == '__main__':
    sys.exit(main())

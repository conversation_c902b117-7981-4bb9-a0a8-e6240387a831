# API 参考文档

本文档描述了 ChestnutCMS RAG 集成系统的所有 API 端点。

## 基础信息

- **基础 URL**: `http://localhost:8000`
- **API 版本**: `v1`
- **API 前缀**: `/api/v1`

## 认证

当前系统无需认证，所有端点均为公开访问。

## 通用响应格式

### 成功响应

```json
{
  "success": true,
  "message": "操作成功",
  "data": { ... }
}
```

### 错误响应

```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "details": "详细错误信息"
}
```

## 端点分类

### 1. 查询端点

#### POST /api/v1/query

执行 RAG 查询，返回 AI 生成的回答和相关文档片段。

**请求体:**

```json
{
  "query": "用户问题",
  "max_results": 5
}
```

**响应:**

```json
{
  "answer": "AI生成的回答",
  "sources": [
    {
      "content": "相关文档片段",
      "score": 0.85,
      "metadata": {
        "content_id": "12345",
        "title": "文章标题",
        "file_url": "https://www.gzmdrw.cn/path/12345.shtml",
        "publish_date": "2024-01-01",
        "filename": "12345.txt"
      }
    }
  ],
  "processing_time": 1.2,
  "total_sources": 3
}
```

### 2. 文档管理端点

#### POST /api/v1/documents/load

从 data 目录重新加载所有文档到向量数据库。

**请求:** 无需请求体

**响应:**

```json
{
  "success": true,
  "message": "成功处理 5 个文件",
  "documents_processed": 5,
  "replaced_files": [
    {
      "filename": "12345.txt",
      "old_chunks": 3,
      "new_chunks": 4
    }
  ],
  "new_files": ["67890.txt"],
  "processing_time": 2.5
}
```

#### GET /api/v1/documents/list

获取系统中所有文档的列表。

**响应:**

```json
{
  "success": true,
  "message": "找到 10 个文档",
  "documents": [
    {
      "filename": "12345.txt",
      "chunks_count": 4,
      "file_size": 2048,
      "file_path": "data/12345.txt"
    }
  ],
  "total_chunks": 40
}
```

#### POST /api/v1/documents/upload

上传单个文档文件。

**请求:** `multipart/form-data`

- `file`: 文件对象（仅支持.txt 文件）

**响应:**

```json
{
  "success": true,
  "message": "文档上传成功: test.txt",
  "filename": "test.txt",
  "replaced": false,
  "old_chunks": 0,
  "new_chunks": 3,
  "total_chunks": 43,
  "file_path": "data/test.txt"
}
```

#### DELETE /api/v1/documents/{filename}

删除指定文档。

**路径参数:**

- `filename`: 要删除的文件名

**响应:**

```json
{
  "success": true,
  "message": "文档删除成功: test.txt (包括磁盘文件)",
  "filename": "test.txt",
  "deleted_chunks": 3,
  "file_deleted_from_disk": true,
  "total_chunks": 40
}
```

### 3. ChestnutCMS 同步端点

#### POST /api/v1/sync/chestnut-cms

同步 ChestnutCMS 文章到 RAG 系统。

**请求:** 无需请求体

**响应:**

```json
{
  "success": true,
  "message": "同步完成! 处理了 15 篇文章",
  "statistics": {
    "total_processed": 15,
    "added": 5,
    "updated": 3,
    "deleted": 2,
    "skipped": 10,
    "errors": 0
  },
  "processing_time": 120.5,
  "errors": [],
  "processed_details": [
    {
      "success": true,
      "content_id": "12345",
      "title": "测试文章",
      "operation": "add",
      "message": "文章处理成功: 测试文章",
      "file_url": "https://www.gzmdrw.cn/news/12345.shtml",
      "chunks": 4
    }
  ]
}
```

#### GET /api/v1/sync/status

获取当前同步状态。

**响应:**

```json
{
  "is_syncing": false,
  "current_progress": 15,
  "total_items": 15,
  "current_item": "同步完成",
  "start_time": "2024-01-01T12:00:00",
  "estimated_completion": null,
  "errors": []
}
```

### 4. 系统状态端点

#### GET /api/v1/status

获取系统健康状态。

**响应:**

```json
{
  "status": "ok",
  "documents_count": 40,
  "storage_size": "15.23MB",
  "collection_name": "documents",
  "data_directory": "./data"
}
```

## 错误代码

| 错误代码             | HTTP 状态码 | 描述                 |
| -------------------- | ----------- | -------------------- |
| `QUERY_FAILED`       | 400         | 查询执行失败         |
| `DOCUMENT_NOT_FOUND` | 404         | 文档不存在           |
| `INVALID_FILE_TYPE`  | 400         | 不支持的文件类型     |
| `SYNC_IN_PROGRESS`   | 409         | 同步正在进行中       |
| `DATABASE_ERROR`     | 500         | 数据库连接或查询错误 |
| `INTERNAL_ERROR`     | 500         | 内部服务器错误       |

## 使用示例

### JavaScript 查询示例

```javascript
async function queryRAG(question) {
  const response = await fetch("/api/v1/query", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      query: question,
      max_results: 5,
    }),
  });

  const result = await response.json();
  return result;
}
```

### Python 同步示例

```python
import requests

def sync_chestnut_cms():
    response = requests.post('http://localhost:8000/api/v1/sync/chestnut-cms')
    return response.json()

# 检查同步状态
def check_sync_status():
    response = requests.get('http://localhost:8000/api/v1/sync/status')
    return response.json()
```

### curl 文档上传示例

```bash
# 上传文档
curl -X POST "http://localhost:8000/api/v1/documents/upload" \
  -F "file=@example.txt"

# 删除文档
curl -X DELETE "http://localhost:8000/api/v1/documents/example.txt"
```

## 性能考虑

- **查询响应时间**: 通常在 1-3 秒内
- **文档上传**: 小文件（<1MB）通常在 1 秒内完成
- **ChestnutCMS 同步**: 首次同步可能需要 5-30 分钟，取决于文章数量
- **并发限制**: 同时只能进行一个同步操作

## 版本历史

- **v1.0**: 初始版本，包含基础 RAG 查询和文档管理
- **v1.1**: 新增 ChestnutCMS 同步功能，扩展元数据支持

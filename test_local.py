#!/usr/bin/env python3
"""
本地测试脚本
专门用于测试与指定联系人的自动回复功能
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import config
from app.services import wx_service, api_client
from app.utils import setup_logger


def test_api_connection():
    """测试API连接"""
    print("🔍 测试API连接...")
    
    if api_client.health_check():
        print("✅ API服务连接正常")
        return True
    else:
        print("❌ API服务连接失败")
        print(f"   API地址: {config.get_api_url()}")
        print("   请检查API服务是否启动")
        return False


def test_config():
    """测试配置"""
    print("🔍 检查配置...")
    
    # 验证配置
    errors = config.validate_config()
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"   - {error}")
        return False
    
    # 显示监听目标
    targets = config.get_enabled_targets()
    print(f"✅ 配置验证通过")
    print(f"📋 监听目标: {len(targets)} 个")
    
    for target in targets:
        print(f"   - {target.name} ({target.chat_type})")
        if target.custom_prompt:
            print(f"     自定义提示: {target.custom_prompt[:50]}...")
    
    return True


def start_test_service():
    """启动测试服务"""
    print("\n" + "="*60)
    print("🚀 启动微信自动回复测试服务")
    print("="*60)
    
    # 显示测试信息
    print(f"📱 测试联系人: 冯子晋1568512996")
    print(f"🔗 API服务: {config.get_api_url()}")
    print(f"⏱️  回复延时: {config.reply_config.min_delay}-{config.reply_config.max_delay}秒")
    print(f"🚦 速率限制: {config.reply_config.max_replies_per_minute}条/分钟")
    
    print("\n" + "-"*60)
    print("📝 测试说明:")
    print("1. 请确保微信客户端已登录")
    print("2. 使用另一个微信号向'冯子晋1568512996'发送消息")
    print("3. 观察系统是否自动回复")
    print("4. 按 Ctrl+C 停止测试")
    print("-"*60)
    
    # 启动服务
    if wx_service.start():
        print("\n✅ 服务启动成功！开始监听消息...")
        print("💡 提示: 可以查看 logs/wxauto_ml.log 获取详细日志")
        
        try:
            # 保持运行并显示状态
            start_time = time.time()
            message_count = 0
            
            while True:
                time.sleep(5)  # 每5秒显示一次状态
                
                # 获取服务状态
                status = wx_service.get_status()
                elapsed_time = int(time.time() - start_time)
                
                # 清屏并显示状态
                print(f"\r⏰ 运行时间: {elapsed_time//60:02d}:{elapsed_time%60:02d} | "
                      f"API状态: {'🟢' if status['api_health'] else '🔴'} | "
                      f"监听目标: {len(status['listen_targets'])} | "
                      f"已处理: {status['message_handler_stats']['processed_messages_count']} 条消息", 
                      end="", flush=True)
                
        except KeyboardInterrupt:
            print("\n\n🛑 接收到停止信号...")
            wx_service.stop()
            print("✅ 测试服务已停止")
            
    else:
        print("❌ 服务启动失败")
        print("💡 请检查:")
        print("   1. 微信客户端是否已登录")
        print("   2. API服务是否正常运行")
        print("   3. 配置文件是否正确")
        return False
    
    return True


def show_test_menu():
    """显示测试菜单"""
    while True:
        print("\n" + "="*50)
        print("🧪 微信自动回复本地测试")
        print("="*50)
        print("1. 测试API连接")
        print("2. 检查配置")
        print("3. 启动测试服务")
        print("4. 查看服务状态")
        print("5. 修改测试配置")
        print("0. 退出测试")
        
        choice = input("\n请选择操作 (0-5): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            test_api_connection()
        elif choice == '2':
            test_config()
        elif choice == '3':
            start_test_service()
        elif choice == '4':
            show_service_status()
        elif choice == '5':
            modify_test_config()
        else:
            print("❌ 无效选择，请重试")
        
        input("\n按回车键继续...")


def show_service_status():
    """显示服务状态"""
    print("\n🔍 服务状态:")
    
    status = wx_service.get_status()
    
    print(f"运行状态: {'🟢 运行中' if status['is_running'] else '🔴 已停止'}")
    print(f"API健康: {'🟢 正常' if status['api_health'] else '🔴 异常'}")
    print(f"配置状态: {'🟢 有效' if status['config_valid'] else '🔴 无效'}")
    
    if status['listen_targets']:
        print(f"监听目标: {', '.join(status['listen_targets'])}")
    else:
        print("监听目标: 无")
    
    # 显示消息处理统计
    stats = status['message_handler_stats']
    print(f"已处理消息: {stats['processed_messages_count']} 条")
    
    if stats['rate_limiter_status']:
        print("速率限制状态:")
        for chat, count in stats['rate_limiter_status'].items():
            print(f"  - {chat}: {count} 条/分钟")


def modify_test_config():
    """修改测试配置"""
    print("\n🔧 修改测试配置:")
    print("1. 修改回复延时")
    print("2. 修改速率限制")
    print("3. 启用/禁用打字模拟")
    print("4. 修改API地址")
    print("0. 返回")
    
    choice = input("请选择 (0-4): ").strip()
    
    if choice == '1':
        try:
            min_delay = float(input(f"最小延时 (当前: {config.reply_config.min_delay}): ") or config.reply_config.min_delay)
            max_delay = float(input(f"最大延时 (当前: {config.reply_config.max_delay}): ") or config.reply_config.max_delay)
            
            config.reply_config.min_delay = min_delay
            config.reply_config.max_delay = max_delay
            config.save_config()
            print("✅ 延时配置已更新")
        except ValueError:
            print("❌ 输入格式错误")
    
    elif choice == '2':
        try:
            max_replies = int(input(f"每分钟最大回复数 (当前: {config.reply_config.max_replies_per_minute}): ") or config.reply_config.max_replies_per_minute)
            
            config.reply_config.max_replies_per_minute = max_replies
            config.save_config()
            print("✅ 速率限制已更新")
        except ValueError:
            print("❌ 输入格式错误")
    
    elif choice == '3':
        current = config.reply_config.enable_typing_simulation
        new_value = input(f"启用打字模拟 (当前: {'是' if current else '否'}) (y/n): ").strip().lower()
        
        if new_value in ['y', 'yes', '是']:
            config.reply_config.enable_typing_simulation = True
        elif new_value in ['n', 'no', '否']:
            config.reply_config.enable_typing_simulation = False
        
        config.save_config()
        print("✅ 打字模拟配置已更新")
    
    elif choice == '4':
        new_url = input(f"API地址 (当前: {config.api_config.base_url}): ").strip()
        if new_url:
            config.api_config.base_url = new_url
            config.save_config()
            print("✅ API地址已更新")


def main():
    """主函数"""
    # 设置日志
    setup_logger(level='INFO')
    
    print("🧪 微信自动回复系统 - 本地测试模式")
    print(f"📋 测试目标: 冯子晋1568512996")
    
    # 显示菜单
    show_test_menu()
    
    print("👋 测试结束，再见！")


if __name__ == '__main__':
    main()

"""
配置管理器
负责配置文件的加载、保存、验证和管理
"""

import json
import os
from typing import Dict, List, Optional
from dataclasses import asdict
from pathlib import Path
from .settings import APIConfig, ReplyConfig, ListenTarget, LogConfig, SystemConfig


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "app/config/config.json"):
        self.config_file = Path(config_file)
        self.api_config = APIConfig()
        self.reply_config = ReplyConfig()
        self.log_config = LogConfig()
        self.system_config = SystemConfig()
        self.listen_targets: List[ListenTarget] = []
        self._load_config()
    
    def _load_config(self):
        """从配置文件加载配置"""
        if not self.config_file.exists():
            self._create_default_config()
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 加载API配置
            if 'api' in config_data:
                self.api_config = APIConfig(**config_data['api'])
            
            # 加载回复配置
            if 'reply' in config_data:
                self.reply_config = ReplyConfig(**config_data['reply'])
            
            # 加载日志配置
            if 'logging' in config_data:
                self.log_config = LogConfig(**config_data['logging'])
            
            # 加载系统配置
            if 'system' in config_data:
                self.system_config = SystemConfig(**config_data['system'])
            
            # 加载监听目标
            if 'listen_targets' in config_data:
                self.listen_targets = [
                    ListenTarget(**target) for target in config_data['listen_targets']
                ]
                
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置文件"""
        default_targets = [
            ListenTarget(name="文件传输助手", enabled=True, auto_reply=True),
        ]
        self.listen_targets = default_targets
        self.save_config()
    
    def save_config(self):
        """保存配置到文件"""
        config_data = {
            'api': asdict(self.api_config),
            'reply': asdict(self.reply_config),
            'logging': asdict(self.log_config),
            'system': asdict(self.system_config),
            'listen_targets': [asdict(target) for target in self.listen_targets]
        }
        
        # 确保配置目录存在
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    def add_listen_target(self, name: str, chat_type: str = "friend", 
                         enabled: bool = True, auto_reply: bool = True,
                         custom_prompt: Optional[str] = None):
        """添加监听目标"""
        target = ListenTarget(
            name=name,
            enabled=enabled,
            auto_reply=auto_reply,
            chat_type=chat_type,
            custom_prompt=custom_prompt
        )
        self.listen_targets.append(target)
        self.save_config()
    
    def remove_listen_target(self, name: str):
        """移除监听目标"""
        self.listen_targets = [t for t in self.listen_targets if t.name != name]
        self.save_config()
    
    def get_listen_target(self, name: str) -> Optional[ListenTarget]:
        """获取指定的监听目标"""
        for target in self.listen_targets:
            if target.name == name:
                return target
        return None
    
    def get_enabled_targets(self) -> List[ListenTarget]:
        """获取所有启用的监听目标"""
        return [t for t in self.listen_targets if t.enabled]
    
    def update_target_status(self, name: str, enabled: bool = None, 
                           auto_reply: bool = None):
        """更新目标状态"""
        target = self.get_listen_target(name)
        if target:
            if enabled is not None:
                target.enabled = enabled
            if auto_reply is not None:
                target.auto_reply = auto_reply
            self.save_config()
    
    def get_api_url(self) -> str:
        """获取完整的API URL"""
        return f"{self.api_config.base_url}{self.api_config.endpoint}"
    
    def validate_config(self) -> List[str]:
        """验证配置的有效性"""
        errors = []
        
        # 验证API配置
        if not self.api_config.base_url:
            errors.append("API base_url 不能为空")
        
        if not self.api_config.endpoint:
            errors.append("API endpoint 不能为空")
        
        if self.api_config.timeout <= 0:
            errors.append("API timeout 必须大于0")
        
        # 验证回复配置
        if self.reply_config.min_delay < 0:
            errors.append("最小延时不能小于0")
        
        if self.reply_config.max_delay < self.reply_config.min_delay:
            errors.append("最大延时不能小于最小延时")
        
        # 验证监听目标
        if not self.listen_targets:
            errors.append("至少需要配置一个监听目标")
        
        target_names = [t.name for t in self.listen_targets]
        if len(target_names) != len(set(target_names)):
            errors.append("监听目标名称不能重复")
        
        return errors


# 全局配置实例
config = ConfigManager()

"""
回复生成器核心模块
负责生成智能回复内容和回复策略
"""

import time
import random
import logging
from typing import Optional, Dict, Any
from ..config import config


class ReplyGenerator:
    """回复生成器核心类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def process_reply_content(self, content: str) -> Optional[str]:
        """
        处理回复内容
        
        Args:
            content: 原始回复内容
            
        Returns:
            Optional[str]: 处理后的回复内容
        """
        if not content:
            return None
        
        # 去除首尾空白
        content = content.strip()
        
        if not content:
            return None
        
        # 检查长度限制
        max_length = config.reply_config.max_reply_length
        if len(content) > max_length:
            content = content[:max_length] + "..."
            self.logger.warning(f"回复内容过长，已截断到{max_length}字符")
        
        # 处理特殊字符
        content = self._process_special_characters(content)
        
        # 添加个性化元素
        content = self._add_personality(content)
        
        return content
    
    def _process_special_characters(self, content: str) -> str:
        """处理特殊字符"""
        # 处理换行符
        content = content.replace('\\n', '\n')
        
        # 处理制表符
        content = content.replace('\\t', '\t')
        
        # 移除多余的空格
        lines = content.split('\n')
        processed_lines = [line.strip() for line in lines]
        
        return '\n'.join(processed_lines)
    
    def _add_personality(self, content: str) -> str:
        """添加个性化元素"""
        # 可以根据配置添加个性化的回复风格
        # 这里只是示例，实际可以根据需求扩展
        
        # 随机添加一些友好的表情符号
        if random.random() < 0.1:  # 10%的概率
            emojis = ['😊', '👍', '🤔', '💡', '✨']
            emoji = random.choice(emojis)
            content = f"{content} {emoji}"
        
        return content
    
    def calculate_typing_delay(self, content: str) -> float:
        """
        计算打字延时
        
        Args:
            content: 回复内容
            
        Returns:
            float: 延时时间（秒）
        """
        if not config.reply_config.enable_typing_simulation:
            return 0.0
        
        # 基础延时
        base_delay = random.uniform(
            config.reply_config.min_delay,
            config.reply_config.max_delay
        )
        
        # 根据内容长度调整延时
        content_length = len(content) if content else 0
        
        # 每100个字符增加0.5-1.5秒延时
        length_factor = (content_length / 100) * random.uniform(0.5, 1.5)
        
        # 最大延时不超过10秒
        total_delay = min(base_delay + length_factor, 10.0)
        
        return total_delay
    
    def should_add_context(self, msg_info: Dict[str, Any]) -> bool:
        """
        判断是否应该添加上下文信息
        
        Args:
            msg_info: 消息信息
            
        Returns:
            bool: 是否添加上下文
        """
        # 群聊消息通常需要更多上下文
        if msg_info.get('chat_type') == 'group':
            return True
        
        # 长消息可能需要上下文
        content_length = len(msg_info.get('content', ''))
        if content_length > 100:
            return True
        
        return False
    
    def generate_context_prompt(self, msg_info: Dict[str, Any]) -> str:
        """
        生成上下文提示
        
        Args:
            msg_info: 消息信息
            
        Returns:
            str: 上下文提示
        """
        context_parts = []
        
        # 添加聊天类型信息
        chat_type = msg_info.get('chat_type', 'friend')
        if chat_type == 'group':
            context_parts.append("这是一个群聊消息")
        else:
            context_parts.append("这是一个私聊消息")
        
        # 添加发送者信息
        sender = msg_info.get('sender', '')
        if sender:
            context_parts.append(f"发送者是{sender}")
        
        # 添加聊天名称
        chat_name = msg_info.get('chat_name', '')
        if chat_name and chat_name != sender:
            context_parts.append(f"聊天对象是{chat_name}")
        
        return "，".join(context_parts) + "。"
    
    def generate_error_reply(self, error_type: str = "general") -> str:
        """
        生成错误回复
        
        Args:
            error_type: 错误类型
            
        Returns:
            str: 错误回复内容
        """
        error_replies = {
            "api_error": [
                "抱歉，我现在有点忙，请稍后再试。",
                "系统暂时无法处理您的消息，请稍后重试。",
                "服务暂时不可用，请稍后再试。"
            ],
            "timeout": [
                "处理时间过长，请稍后再试。",
                "响应超时，请重新发送消息。"
            ],
            "rate_limit": [
                "消息发送过于频繁，请稍后再试。",
                "请稍等一下再发送消息。"
            ],
            "general": [
                "抱歉，我暂时无法回复您的消息。",
                "系统出现了一些问题，请稍后再试。"
            ]
        }
        
        replies = error_replies.get(error_type, error_replies["general"])
        return random.choice(replies)
    
    def generate_welcome_reply(self, chat_type: str = "friend") -> str:
        """
        生成欢迎回复
        
        Args:
            chat_type: 聊天类型
            
        Returns:
            str: 欢迎回复内容
        """
        if chat_type == "group":
            welcome_replies = [
                "大家好！我是智能助手，有什么可以帮助大家的吗？",
                "Hello！很高兴加入这个群聊，有问题随时找我哦～",
                "Hi！我来了，有什么需要帮助的请告诉我！"
            ]
        else:
            welcome_replies = [
                "您好！我是智能助手，有什么可以帮助您的吗？",
                "Hello！很高兴为您服务，请问有什么需要帮助的？",
                "Hi！有什么问题可以随时问我哦～"
            ]
        
        return random.choice(welcome_replies)
    
    def is_greeting_message(self, content: str) -> bool:
        """
        判断是否为问候消息
        
        Args:
            content: 消息内容
            
        Returns:
            bool: 是否为问候消息
        """
        if not content:
            return False
        
        content_lower = content.lower().strip()
        
        greetings = [
            'hello', 'hi', 'hey', '你好', '您好', '早上好', '下午好', 
            '晚上好', '晚安', '再见', 'bye', 'goodbye'
        ]
        
        for greeting in greetings:
            if greeting in content_lower:
                return True
        
        return False
    
    def generate_greeting_reply(self, content: str) -> str:
        """
        生成问候回复
        
        Args:
            content: 原始问候内容
            
        Returns:
            str: 问候回复
        """
        content_lower = content.lower().strip()
        
        if any(word in content_lower for word in ['早上好', '早']):
            return random.choice(['早上好！', '早安！', '新的一天开始了，加油！'])
        elif any(word in content_lower for word in ['下午好', '午安']):
            return random.choice(['下午好！', '午安！', '下午愉快！'])
        elif any(word in content_lower for word in ['晚上好', '晚安']):
            return random.choice(['晚上好！', '晚安！', '祝您好梦！'])
        elif any(word in content_lower for word in ['再见', 'bye', 'goodbye']):
            return random.choice(['再见！', '拜拜！', '期待下次聊天！'])
        else:
            return random.choice(['你好！', '您好！', 'Hello！', 'Hi！'])


# 全局回复生成器实例
reply_generator = ReplyGenerator()

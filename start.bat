@echo off
chcp 65001 >nul
title 微信自动回复系统

echo ========================================
echo 微信自动回复系统启动脚本
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist ".venv" (
    echo 警告: 未找到虚拟环境，建议使用虚拟环境运行
    echo.
)

REM 激活虚拟环境（如果存在）
if exist ".venv\Scripts\activate.bat" (
    echo 激活虚拟环境...
    call .venv\Scripts\activate.bat
)

REM 检查依赖
echo 检查依赖包...
python -c "import wxauto, requests" >nul 2>&1
if errorlevel 1 (
    echo 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 运行系统测试
echo.
echo 运行系统测试...
python app\test_system.py
if errorlevel 1 (
    echo.
    echo 警告: 系统测试未完全通过，但仍可尝试启动服务
    echo.
)

REM 启动服务
echo.
echo 启动微信自动回复服务...
echo 按 Ctrl+C 可停止服务
echo.
python run.py start

pause
